<template>
  <div id="app">
    <a-config-provider :locale="locale">
      <router-view />
      <theme-picker />
    </a-config-provider>
  </div>
</template>

<script>
import ThemePicker from "@/components/ThemePicker";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getEnums } from "@/api/enums";
import moment from "moment";
import { setAllDicts } from "@/utils/storage";
import "moment/locale/zh-cn";
moment.locale("zh-cn");
export default {
  name: "App",
  created() {
    const loginPages = ["/login"]; // 需要排除的页面
    if (loginPages.includes(this.$route.path)) {
      // 当前是登录页，跳过刷新检测
      return;
    }
    const entries = performance?.getEntriesByType("navigation");
    if (entries && entries.length > 0) {
      const navigationType = entries[0].type;
      if (navigationType === "reload") {
        getEnums().then((res) => {
          setAllDicts(res.data.data);
        });
      }
    }
  },
  data() {
    return {
      locale: zhCN,
    };
  },
  components: { ThemePicker },
  metaInfo() {
    return {
      title:
        this.$store.state.settings.dynamicTitle &&
        this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title
          ? `${title} - ${process.env.VUE_APP_TITLE}`
          : process.env.VUE_APP_TITLE;
      },
    };
  },
  methods: {
    handleBeforeUnload(event) {
      console.log("页面刷新或关闭被检测到！");
      // 可以在这里执行一些保存状态的操作
    },
  },
};
</script>
<style scoped>
#app .theme-picker {
  display: none;
}
/deep/ .kun-content .low-code-renderer {
  min-height: calc(100vh - 84px - 40px);
}
</style>
