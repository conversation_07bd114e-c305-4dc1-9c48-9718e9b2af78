import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { removeYopToken, removeMagicToken } from '@/utils/storage'
import { setUserName } from '@/utils/user'
import { doSm2Encrypt, privateKey } from '@/utils/crypto'
import { setLocalStorage, setYopToken, setMagicToken } from '@/utils/storage'
const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      const emailCode = userInfo.emailCode
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid, emailCode).then(res => {
          //响应体不一样
          if (res.code === 200) {
            setToken(res.token)
            setUserName(username)
            // 调整yop需要
            setYopToken(res.token)
            // 调整magic需要
            setMagicToken(res.token)
            commit('SET_TOKEN', res.token)
            setLocalStorage('cnt', privateKey.slice(4)+privateKey.slice(0,4))
            resolve()
          } else {
            reject(res.data)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.user
          const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/profile.png") : process.env.VUE_APP_BASE_API + user.avatar;
          if (res.roles && res.roles.length > 0) { // 验证返回的roles是否是一个非空数组
            commit('SET_ROLES', res.roles)
            // 加密存本地一份
            setLocalStorage('roles', res.roles)
            commit('SET_PERMISSIONS', res.permissions)
            // 加密存本地一份
            setLocalStorage('cyd', doSm2Encrypt(JSON.stringify(res.permissions)))
          } else {
            commit('SET_ROLES', ['ROLE_DEFAULT'])
          }
          commit('SET_NAME', user.userName)
          commit('SET_AVATAR', avatar)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          removeToken()
          // 移除yop token
          removeYopToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        removeToken()
        // 移除yop token
        removeYopToken()
        // 移除magic token
        removeMagicToken()
        resolve()
      })
    },
    // 设置Token
    SetToken({commit}, token) {
      commit('SET_TOKEN', token)
    }
  }
}

export default user
