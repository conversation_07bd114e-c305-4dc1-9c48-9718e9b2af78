import { getTaskList } from "@/api/reconciliation";

const processData = (data) => {
  return data.reduce((result, item) => {
    // 查找是否已经存在相同 businessCode 的项
    let business = result.find((b) => b.value === item.businessCode);

    if (!business) {
      // 如果不存在，则创建一个新的 business 结构
      business = {
        label: item.businessName,
        value: item.businessCode,
        bizType: [],
      };
      result.push(business);
    }

    // 查找 bizType 是否已存在
    let bizTypeItem = business.bizType.find((b) => b.value === item.bizType);

    if (!bizTypeItem) {
      // 如果不存在，创建新的 bizType 结构
      bizTypeItem = {
        label: item.bizTypeName,
        value: item.bizType,
        fetchConfigNo: item.fetchConfigNo,
        fetchHandle: [],
        dataFormats: [],
      };
      business.bizType.push(bizTypeItem);
    }

    // 添加 fetchHandle（去重）
    if (!bizTypeItem.fetchHandle.some((f) => f.value === item.fetchHandle)) {
      bizTypeItem.fetchHandle.push({
        label: item.fetchHandle,
        value: item.fetchHandle,
      });
    }

    // 添加 dataFormats（去重）
    if (!bizTypeItem.dataFormats.some((d) => d.value === item.fetchType)) {
      bizTypeItem.dataFormats.push({
        label: item.fetchType,
        value: item.fetchType,
      });
    }

    return result;
  }, []);
};

function extractUniqueBizTypes(data) {
  if (!data) return [];
  const tasks = data.filter((item) =>
    ["ID10001", "ID10002", "ED10007"].includes(item.businessCode)
  );

  const uniqueMap = new Map();

  tasks.forEach((item) => {
    if (!uniqueMap.has(item.bizType)) {
      uniqueMap.set(item.bizType, {
        value: item.bizType,
        label: item.bizTypeName,
        raw: {
          listClass: "default",
        },
      });
    }
  });

  return Array.from(uniqueMap.values());
}

const task = {
  namespaced: true,
  state: {
    taskList: [], // 拉取任务
    bizTypeList: [], // 交易类型
  },
  mutations: {
    SET_TASK: (state, taskList) => {
      state.taskList = taskList;
    },
    SET_BIZ: (state, bizTypeList) => {
      state.bizTypeList = bizTypeList;
    },
  },
  actions: {
    // 获取对账任务
    getTaskList({ commit, state }) {
      return new Promise((resolve, reject) => {
        if (state.taskList && state.taskList.length) {
          resolve({
            taskList: state.taskList,
            bizTypesList: state.bizTypeList,
          });
        } else {
          getTaskList()
            .then((res) => {
              const list = res.data?.data?.list;
              const taskList = processData(list);
              const bizTypesList = extractUniqueBizTypes(list);
              resolve({
                taskList,
                bizTypesList,
              });
              commit("SET_TASK", taskList);
              commit("SET_BIZ", bizTypesList);
            })
            .catch((error) => {
              reject(error);
            });
        }
      });
    },
  },
};

export default task;
