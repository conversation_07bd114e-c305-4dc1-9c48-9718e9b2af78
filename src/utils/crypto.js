// 使用require导入
var smCrypto = require("sm-crypto");

const sm2 = smCrypto.sm2;
const cipherMode = 1; // 1 - C1C3C2，0 - C1C2C3，默认为1
const publicKey = '04e16ece8cad09aff45dbe7ba09674e9b94578a91a5ed6e8ee0f49fb54589204ab63c8a634e00f950e0c583d6e3091d76084e5c84fa0ce9aa4f49db304c2d5867a';
const privateKey = 'c9fb5d3cc2cb71f164fc641f96368acccd0c9f306e0fe05c4805b333c8749924';
 
export {
    publicKey,
    privateKey
};
/**
 * 国密加解密工具类
 */
// SM2加密
export function doSm2Encrypt(msgString) {
  return sm2.doEncrypt(msgString, publicKey, cipherMode);
}
// SM2解密
export function doSm2Decrypt(encryptData) {
  return sm2.doDecrypt(encryptData, privateKey, cipherMode);
}
// SM2数组加密
export function doSm2ArrayEncrypt(msgString) {
  return sm2.doEncrypt(msgString, publicKey, cipherMode);
}
// SM2数组解密
export function doSm2ArrayDecrypt(encryptData) {
  return sm2.doDecrypt(encryptData, privateKey, cipherMode, { output: 'array' });
}
// // 要加密的明文
// var plaintext = "Hello, world!";
// // 使用公钥进行加密
// var ciphertext = doSm2Encrypt(plaintext, publicKey);

// var decryptedText = doSm2Decrypt(ciphertext, privateKey);
// console.log('decryptedText:', decryptedText);
