// import { createService } from '@/utils/request'
// const request = createService('/pmc-server')

const fetchSchema = async (pageCode) => {
  const origin = window.location.origin;
  // lowcode
  // let jsonUrl = `http://imgqa.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`;
    // 本地测试使用
    let jsonUrl = `https://img.yeepay.com/pmc-static/pmc/page/config/${pageCode}_SNAPSHOT.json`;

  // 项目域名和文件指针映射关系
  const originUrlMapping = {
    // 生产
    "https://admin.kun.global": `https://img.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    "http://admin.kun.global": `http://img.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    // 内测
    "http://adminnc.kun.global": `http://imgnc.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    "https://adminnc.kun.global": `https://imgnc.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    // qa
    "http://adminqa.kun.global": `http://imgqa.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    "https://adminqa.kun.global": `https://imgqa.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    // dev
    "http://admindev.kun.global": `http://imgdev.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    "https://admindev.kun.global": `https://imgdev.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    // 新加坡
    "http://adminsg.kun.global": `http://imgqa.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
    "https://adminsg.kun.global": `https://imgqa.kun.global/lowcode/pointer/${pageCode}_RELEASE.json`,
  };
  jsonUrl = originUrlMapping[origin] || jsonUrl;

  return fetch(jsonUrl, {
    cache: "no-cache",
  }).then((response) => {
    return response.json().then((data) => {
      if (data && data.newestContentUrl) {
        return fetch(data.newestContentUrl).then((res) => {
          return res.json().then((data) => {
            return data;
          });
        });
      }
    });
  });
};

export { fetchSchema };
