const DictKey = "kun_dicts";
const yopTokenKey = "accesstoken";
const magicTokenKey = "yuiassotoken";
export function setAllDicts(dicts) {
  return localStorage.setItem(DictKey, JSON.stringify(dicts));
}

export function getAllDicts() {
  return JSON.parse(localStorage.getItem(DictKey));
}

export function findDictsByType(type) {
  const dicts = getAllDicts();
  return dicts
    .find((item) => item.dictType === type)
    ?.items.map((item) => {
      return {
        label: item.name,
        value: item.code,
        raw: {
          listClass: "default",
        },
      };
    });
}

export function removeAllDicts() {
  return localStorage.removeItem(DictKey);
}

// 通用的localStorage存储方法
export function setLocalStorage(key, dicts) {
  return localStorage.setItem(key, JSON.stringify(dicts));
}

export function getLocalStorage(key) {
  return localStorage.getItem(key);
}

export function removeLocalStorage(key) {
  return localStorage.removeItem(key);
}

// yop存token
export function setYopToken(token) {
  return localStorage.setItem(yopTokenKey, token);
}

export function getYopToken() {
  return localStorage.getItem(yopTokenKey);
}

export function removeYopToken() {
  return localStorage.removeItem(yopTokenKey);
}
// magic存token
export function setMagicToken(token) {
  return localStorage.setItem(magicTokenKey, token);
}

export function getMagicToken() {
  return localStorage.getItem(magicTokenKey);
}

export function removeMagicToken() {
  return localStorage.removeItem(magicTokenKey);
}
