import request from '@/utils/request'
import requestDownloadNoTimeout from '@/utils/requestDownloadNoTimeout'

// 可用配置列表查询
export function configList(data) {
    return request({
        url: '/admin/wf/config/list',
        method: 'post',
        data: data
    })
}
// 根据userid查询用户基本信息
export function userDetail(query) {
    return request({
        url: '/user/detail/base',
        method: 'get',
        params: query
    })
}

// 获取量化结算工单客户id配置列表
export function getCustomerNoList() {
  return request({
    url: '/quant/getCustomerNoList',
    method: 'get'
  })
}

// 提单
export function apply(data) {
    return request({
        url: '/admin/wf/data/apply',
        method: 'post',
        data: data
    })
}
// 提单
export function detail(data) {
    return request({
        url: '/admin/wf/data/detail',
        method: 'post',
        data: data
    })
}
// 审核（通过/拒绝）
export function review(data) {
    return request({
        url: '/admin/wf/data/review',
        method: 'post',
        data: data
    })
}
// 工单角色对应操作人查询
export function roleConfigQuery(data) {
    return request({
        url: '/admin/wf/role/config/query',
        method: 'post',
        data: data
    })
}
// 工单角色对应操作人保存
export function roleConfigSave(data) {
    return request({
        url: '/admin/wf/role/save',
        method: 'post',
        data: data
    })
}
// 下载供应商绑定工单图片文件
export function downloadAuthFile(data) {
    return requestDownloadNoTimeout({
        url: '/userAuth/downloadBatchAuthFile',
        method: 'post',
        data: data,
        responseType: 'blob'
    })
}
// 工单查询渠道账号
export function accountNoList(data) {
  return requestDownloadNoTimeout({
    url: '/admin/wf/data/account-no/list',
    method: 'post',
    data: data,
  })
}

// 获取所有生效通道
export function getChannelEffect(data) {
  return requestDownloadNoTimeout({
    url: '/channel/query/effect',
    method: 'post',
    data: data,
  })
}
