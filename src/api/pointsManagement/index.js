import request from "@/utils/request";

// 新增积分规则
export function addPointsRule(data) {
  return request({
    url: "/points/rule/add",
    method: "post",
    data: data,
  }); 
}


// 更新积分规则
export function updatePointsRule(data) {
  return request({
    url: "/points/rule/update",
    method: "post",
    data: data,
  }); 
}


// 获取积分规则详情
export function getPointsRuleDetail(data) {
  return request({
    url: `/points/rule/detail`,
    method: "post",
    data: data,
  });
}