import request from '@/utils/request'

// 审核
export function review(data) {
  return request({
    url: '/user/form/data/orderReview',
    method: 'post',
    data: data
  })
}

// 查询用户临时表单详情
export function getAdjustmentDetail(data) {
  return request({
    url: '/user/form/data/tempDetail',
    method: 'post',
    data: data
  })
}

// 保存调单
export function saveAdjustment(data) {
  return request({
    url: '/user/form/data/save',
    method: 'post',
    data: data
  })
}

// 提交调单
export function submitAdjustment(data) {
  return request({
    url: '/user/form/data/submit',
    method: 'post',
    data: data
  })
}

// 查询调单详情
export function getAdjustmentDetailPage(data) {
  return request({
    url: '/user/form/data/detail',
    method: 'post',
    data: data
  })
}

// 查询调单详情列表
export function getAdjustmentDetailList(data) {
  return request({
    url: '/user/form/data/detailList',
    method: 'post',
    data: data
  })
}

// 查询调单详情
export function getAdjustmentDetailInfo(data) {
  return request({
    url: '/user/form/data/detail/info',
    method: 'post',
    data: data
  })
}
