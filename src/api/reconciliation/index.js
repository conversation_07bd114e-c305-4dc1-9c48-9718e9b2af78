import request from "@/utils/request";

// 差错处理页面
export function checkMistakeQuery(data) {
  return request({
    url: "/check/query/mistake/list",
    method: "post",
    data: data,
  });
}

// 拉取任务查询
export function checkFetchTaskQuery(data) {
  return request({
    url: "/fetch/order/list",
    method: "post",
    data: data,
  });
}

// 拉取任务新增
export function addFetchTask(data) {
  return request({
    url: "/fetch/add",
    method: "post",
    data: data,
  });
}

// 拉取任务重试
export function taskRetry(data) {
  return request({
    url: "/fetch/retry",
    method: "post",
    data: data,
  });
}

// 对账任务查询
export function checkTaskQuery(data) {
  return request({
    url: "/check/query/task/list",
    method: "post",
    data: data,
  });
}

// 对账任务新增
export function checkTaskAdd(data) {
  return request({
    url: "/check/manual",
    method: "post",
    data: data,
  });
}

// 对账任务重试
export function checkTaskRetry(data) {
  return request({
    url: "/check/retry",
    method: "post",
    data: data,
  });
}

// 处理差错
export function checkMistakeDeal(data) {
  return request({
    url: "/check/mistake/deal",
    method: "post",
    data: data,
  });
}

// 上传文件
export function checkFileUpload(data) {
  return request({
    url: "/check/file/upload",
    method: "post",
    data: data,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

// 渠道余额核对
export function checkBalanceQuery(data) {
  return request({
    url: "/check/query/balance",
    method: "post",
    data: data,
  });
}

// 对账结果汇总查询
export function checkSummaryQuery(data) {
  return request({
    url: "/check/query/summary",
    method: "post",
    data: data,
  });
}

// 渠道余额核对-处理
export function checkBalanceDeal(data) {
  return request({
    url: "/check/deal/balance",
    method: "post",
    data: data,
  });
}

// 拉取任务配置联动查询
export function getTaskList(data) {
  return request({
    url: "/fetch/query/config",
    method: "post",
    data: data,
  });
}
