import request from "@/utils/request";

// 添加租户
export function addTenant(data) {
  return request({
    url: "/waas/tenant/add",
    method: "post",
    data: data,
  });
}

// 更新租户
export function updateTenant(data) {
  return request({
    url: "/waas/tenant/update",
    method: "post",
    data: data,
  });
}

// 获取租户详情
export function tenantDetail(data) {
  return request({
    url: "/waas/tenant/getTenantDetail",
    method: "post",
    data: data,
  });
}

// 查询租户列表
export function tenantList(data) {
  return request({
    url: "/waas/tenant/list",
    method: "post",
    data: data,
  });
}

// 添加租户配置
export function addTenantConfig(data) {
  return request({
    url: "/waas/tenant/config/add",
    method: "post",
    data: data,
  });
}

// 修改配置管理
export function updateTenantConfig(data) {
  return request({
    url: "/waas/tenant/config/update",
    method: "post",
    data: data,
  });
}