<template>
  <el-select
    style="width: 100%"
    :value="value"
    :loading="loading"
    :multiple="multiple"
    :placeholder="placeholder"
    clearable
    @change="taskChange"
  >
    <el-option
      v-for="dict in taskList"
      :key="dict[alias]"
      :label="dict.label"
      :value="dict[alias]"
    />
  </el-select>
</template>

<script>
import { findDictsByType } from "@/utils/storage";
export default {
  props: {
    multiple: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String | Array,
      default: null,
    },
    alias: {
      type: String,
      default: "value",
    },
    // pull 是拉取任务，reconciliation 任务名称
    pageType: {
      type: String,
      default: "pull",
    },
    placeholder: {
      type: String,
      default: "请选择任务名称",
    },
  },

  created() {
    this.loading = true;
    if (this.pageType === "reconciliation") {
      this.taskList = findDictsByType("check_task_name");
      this.loading = false;
    } else {
      this.$store
        .dispatch("task/getTaskList")
        .then((res) => {
          this.taskList = res.taskList;
          this.loading = false;
        })
        .catch((err) => {
          console.log(err, "err");
          this.loading = false;
        });
    }
  },
  data() {
    return {
      loading: true,

      taskList: [],
    };
  },
  methods: {
    taskChange(value) {
      this.$emit("input", value);
      if (Array.isArray(value)) {
        const item = this.taskList.find((item) => item[this.alias] === value);
      }
      const item = this.taskList.find((item) => item[this.alias] === value);

      this.$emit("taskChange", item);
    },
  },
};
</script>

<style></style>
