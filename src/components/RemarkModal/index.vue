<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="600px"
    @close="closeModal"
    top="5vh"
    custom-class="el-dialog-underline"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="remarkForm" :rules="rules" ref="remarkForm" size="small">
      <el-form-item label="备注" :required="required" prop="remark">
        <el-input
          v-model="remarkForm.remark"
          placeholder="请输入备注"
          clearable
          maxlength="50"
          show-word-limit
          size="small"
          style="margin-bottom: 20px"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        :loading="retryLoading"
        @click="handleRetryConfirm"
        >确 定</el-button
      >
      <el-button @click="visible = false">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "RemarkModal",
  props: {
    title: {
      type: String,
      default: "重试",
    },
    required: {
      type: Boolean,
      default: true,
    },
    addRemark: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      retryLoading: false,
      remarkForm: {
        remark: "",
      },
    };
  },
  computed: {
    rules: function () {
      return {
        remark: [
          {
            required: this.required,
            message: "备注不能为空",
            trigger: "change",
          },
          { required: this.required, message: "备注不能为空", trigger: "blur" },
        ],
      };
    },
  },
  methods: {
    show() {
      this.visible = true;
    },
    closeModal() {
      this.resetForm("remarkForm");
    },
    handleRetryConfirm() {
      this.$refs["remarkForm"].validate(async (valid) => {
        if (valid) {
          try {
            this.retryLoading = true;
            const data = await this.addRemark(this.remarkForm);
            this.visible = false;
            this.closeModal();
            this.$emit("success");
          } catch (error) {
            console.log(error, "er");
          } finally {
            this.retryLoading = false;
          }
        }
      });
    },
  },
};
</script>

<style></style>
