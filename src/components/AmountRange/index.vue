<template>
  <div class="amount-range">
    <el-input
      v-model="leftValue"
      placeholder=""
      @input="handleLeftInput"
      @blur="validateRange"
    ></el-input>
    <span class="separator">至</span>
    <el-input
      v-model="rightValue"
      placeholder=""
      @input="handleRightInput"
      @blur="validateRange"
    ></el-input>
  </div>
</template>

<script>
export default {
  name: "AmountRange",
  props: {
    value: {
      type: Array,
      default: () => [null, null], // 默认值为 [null, null]
    },
  },
  data() {
    return {
      leftValue: this.value[0], // 左边的值
      rightValue: this.value[1], // 右边的值
    };
  },
  watch: {
    value(newVal) {
      // 监听外部传入的 value 变化
      this.leftValue = newVal[0];
      this.rightValue = newVal[1];
    },
  },
  methods: {
    // 处理左边输入框的输入
    handleLeftInput(value) {
      this.leftValue = this.validateNumber(value);
    },
    // 处理右边输入框的输入
    handleRightInput(value) {
      this.rightValue = this.validateNumber(value);
    },
    // 验证并调整区间范围
    validateRange() {
      if (this.leftValue !== null && this.rightValue !== null) {
        if (parseFloat(this.leftValue) > parseFloat(this.rightValue)) {
          // 如果左边的值大于右边的值，交换两者的值
          [this.leftValue, this.rightValue] = [this.rightValue, this.leftValue];
        }
      }
      // 触发 v-model 更新
      this.$emit("input", [this.leftValue, this.rightValue]);
    },
    // 验证输入是否为数字（支持小数）
    validateNumber(value) {
      // 只保留数字和小数点
      const validatedValue = value.replace(/[^0-9.]/g, "");
      // 确保只有一个小数点
      const parts = validatedValue.split(".");
      if (parts.length > 2) {
        return parts[0] + "." + parts.slice(1).join("");
      }
      return validatedValue;
    },
  },
};
</script>

<style scoped>
.amount-range {
  display: flex;
  align-items: center;
}

.separator {
  margin: 0 10px;
  font-size: 16px;
}
</style>
