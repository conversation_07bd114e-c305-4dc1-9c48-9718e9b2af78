<template>
  <el-date-picker
    v-model="innerValue"
    style="width: 100%"
    value-format="yyyy-MM-dd"
    type="daterange"
    :picker-options="pickerOptions"
    range-separator="-"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    @change="handleChange"
  ></el-date-picker>
</template>

<script>
export default {
  name: "LimitedDatePicker",
  props: {
    value: Array, // v-model 绑定的值
  },
  data() {
    return {
      innerValue: this.value ? [...this.value] : [], // 这里拷贝数组，避免修改 props
      pickerOptions: {
        disabledDate: () => false, // 默认无禁用日期
        onPick: this.handlePick,
      },
    };
  },
  watch: {
    value(newVal) {
      console.log(newVal, "newVal");
      this.innerValue = newVal ? [...newVal] : [];
    },
  },

  methods: {
    handlePick(value) {
      if (value && value.minDate && value.maxDate) {
        // 取消 pickerOptions限制可选范围
        this.pickerOptions = {
          ...this.pickerOptions, // 保持其他配置项
          disabledDate: () => {
            return false;
          },
        };
      } else if (value && value.minDate) {
        const startDate = new Date(value.minDate); // 获取选中的第一个日期
        const minDate = new Date(startDate);
        minDate.setMonth(minDate.getMonth() - 6); // 限制前3个月
        const maxDate = new Date(startDate);
        maxDate.setMonth(maxDate.getMonth() + 6); // 限制后3个月

        // 更新 pickerOptions 限制可选范围
        this.pickerOptions = {
          ...this.pickerOptions, // 保持其他配置项
          disabledDate: (date) => {
            return date < minDate || date > maxDate;
          },
        };
      }
    },
    // 手动触发 v-model 更新
    handleChange(value) {
      this.$emit("input", value);
    },
  },
};
</script>
