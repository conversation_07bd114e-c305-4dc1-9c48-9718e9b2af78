<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  name: 'ChannelFundBusiness',
  data() {
    return {
      pageCode: 'channel_fund_business',
      schema: {} ,
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer
      v-if="schema.components"
      :key="pageCode"
      :schema="schema"
    />
  </div>
</template>
