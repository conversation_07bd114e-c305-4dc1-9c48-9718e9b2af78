<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'limitsModelList',
      schema: {}
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    toBack() {
      this.$tab.closePage()
    }
  },
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema" @toBack="toBack"/>
  </div>
</template>
