<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'channel_deposit_withdrawal_configured',
      schema: {}
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    goAddPage () {
        this.$router.push('/channelManage/addChannel')
    },
    goEditPage (channelId, row) {
        this.$router.push('/channelManage/editChannel?id=' + channelId)
    }

  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer
      v-if="schema.components"
      :key="pageCode"
      @goAddPage="goAddPage"
      @goEditPage="goEditPage"
      :schema="schema"
    />
  </div>
</template>
