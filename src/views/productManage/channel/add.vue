<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'channel_deposit_withdrawal_config_add',
      schema: {}
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    goList () {
        this.$tab.closeOpenPage({
          path: '/channelManage/channelConfigurable'
          }
        )
    }
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer
      v-if="schema.components"
      :key="pageCode"
      @goList="goList"
      :schema="schema"
    />
  </div>
</template>
