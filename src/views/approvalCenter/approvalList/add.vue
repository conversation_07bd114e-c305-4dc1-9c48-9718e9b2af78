<template>
  <div class="add-audit">
    <el-form ref="queryForm" :model="queryParams" size="small" :inline="true" label-width="110px">
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择类型" clearable>
          <el-option
            v-for="item in configDataList"
            :key="item.wfConfigId"
            :label="item.wfConfigName"
            :value="item.wfConfigId"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <AssetAccountBookkeeping v-if="queryParams.type == 'R0002'" :type="queryParams.type" />
    <UserBalanceDeduction v-else-if="queryParams.type == 'R0001'" :type="queryParams.type" />
    <CAccessAuditOrder v-else-if="queryParams.type == 'R0003'" :type="queryParams.type" />
    <DesignatedUserGas v-else-if="queryParams.type == 'R0004'" :type="queryParams.type" />
    <OpenKunSafe v-else-if="queryParams.type == 'R0005'" :type="queryParams.type" />
    <QuantitativeSettlement v-else-if="queryParams.type == 'R0008'" :type="queryParams.type" />
  </div>
</template>
<script>
import { configList } from '@/api/approvalCenter/approvalList'
import AssetAccountBookkeeping from './orderList/assetAccountBookkeeping.vue'
import UserBalanceDeduction from './orderList/userBalanceDeduction.vue'
import CAccessAuditOrder from './orderList/cAccessAuditOrder.vue'
import DesignatedUserGas from './orderList/designatedUserGas.vue'
import OpenKunSafe from './orderList/openKunSafe.vue'
import QuantitativeSettlement from "./orderList/quantitativeSettlement.vue";
export default {
  name: 'ApprovalAdd',
  components: {
    AssetAccountBookkeeping,
    UserBalanceDeduction,
    CAccessAuditOrder,
    DesignatedUserGas,
    QuantitativeSettlement,
    OpenKunSafe
  },
  data() {
    return {
      queryParams: {
        type: ''
      },
      configDataList: []
    }
  },
  created() {
    configList({}).then(response => {
      this.configDataList = response.data?.data
      this.queryParams.type = response.data?.data[0]?.wfConfigId
    })
  }
}
</script>
<style scoped lang="scss">
.add-audit {
    padding: 20px;
    min-height: calc(100vh - 84px - 40px);
    ::v-deep .el-form {
        display: flex;
        flex-wrap: wrap;
        &>.el-form-item {
            width: 49%;
            display: flex;
            &>.el-form-item__content {
                flex: 1;
                &>.el-input,
                &>.el-select {
                    width: 90%;
                }
            }
        }
    }
}
</style>
