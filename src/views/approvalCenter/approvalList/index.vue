<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import { detail, review, downloadAuthFile } from '@/api/approvalCenter/approvalList'
export default defineComponent({
  name: 'ApprovalList',
  data() {
    return {
      pageCode: 'approvalCenterApprovalList',
      schema: {},
      showOperationDetail: false,
      showViewDetail: false,
      detailData: {},
      wfData: [],
      passLoading: false,
      refuseLoading: false,
      wfId: '',
      wfConfigId: '',
      isShow: true,
      ruleForm: {
        remark: ''
      },
      rules: {
        remark: [
          { required: true, message: '请输入留言', trigger: 'blur' }
        ]
      },
      imageList: [],
      pendingSupplierLoading: false,
      passSupplierLoading: false
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  activated() {
    if (this.$route.query.refresh) {
      this.isShow = false // 刷新页面
      this.$nextTick(() => {
        this.isShow = true
      })
    }
  },
  methods: {
    addApprovalForm() {
      this.$router.push('/approvalCenter/approvalList/add')
    },
    handleDetail(wfId, type) {
      const obj = { wfDataId: wfId }
      if (type === 'detail') {
        obj.onlyQuery = true
      }
      detail(obj).then(response => {
        if (!response.data?.data) {
          this.detailData = {}
          this.detailData.newFlow = []
          this.wfData = []
          this.imageList = []
          return
        }
        this.detailData = response.data.data
        this.detailData.newFlow = this.detailData.flow.filter(i => {
          return i.status !== '-'
        })
        this.wfData = JSON.parse(this.detailData.wfData).map(i => {
          if (i.key === 'auditList' && i.value) {
            const arr = []
            i.value.forEach(j => {
              Object.entries(j).forEach(k => {
                arr.push({
                  label: k[0].split('_')[1],
                  value: k[1]
                })
              })
            })
            i.list = arr
          }
          return i
        })
        if (this.detailData.wfConfigId === 'R0006') {
          const list = this.wfData.filter(i => i.key === 'imageList')[0].value
          this.imageList = list ? JSON.parse(list) : []
          this.wfData = this.wfData.filter(i => i.key !== 'imageList')
        }
      })
    },
    toPendingDetail(wfId, wfConfigId) {
      this.wfId = wfId
      this.wfConfigId = wfConfigId
      this.showOperationDetail = true
      this.handleDetail(wfId, 'edit')
    },
    toViewDetail(wfId) {
      this.showViewDetail = true
      this.handleDetail(wfId, 'detail')
    },
    reviewOperation(type) {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          type === 'pass' ? (this.passLoading = true) : (this.refuseLoading = true)
          review({
            wfId: this.wfId,
            wfConfigId: this.wfConfigId,
            reviewResult: type === 'pass' ? 'REVIEW_PASS' : 'REVIEW_REJECT',
            reviewRemark: this.ruleForm.remark
          }).then(response => {
            if (response.data?.code === 'AS000000') {
              this.clearForm()
              this.isShow = false // 刷新页面
              this.$nextTick(() => {
                this.isShow = true
              })
            }
          }).finally(() => {
            type === 'pass' ? (this.passLoading = false) : (this.refuseLoading = false)
          })
        }
      })
    },
    pass() {
      this.reviewOperation('pass')
    },
    refuse() {
      this.reviewOperation('refuse')
    },
    closeDraw(done) {
      this.detailData = {}
      this.detailData.newFlow = []
      this.ruleForm.remark = ''
      this.wfData = []
      this.imageList = []
      done()
    },
    clearForm() {
      this.detailData = {}
      this.detailData.newFlow = []
      this.ruleForm.remark = ''
      this.imageList = []
      this.wfData = []
      this.showOperationDetail = false
    },
    getLabelValue(item) {
      return item.label ? `${item.label}:` : ''
    },
    downloadPendingSupplier() {
      this.pendingSupplierLoading = true
      const filterUserId = this.wfData.filter(i => i.key === 'userId')
      downloadAuthFile({ fileName: this.imageList, userId: filterUserId.length ? filterUserId[0].value : '' })
        .then(response => {})
        .finally(() => {
          this.pendingSupplierLoading = false
        })
    },
    downloadPassSupplier() {
      this.passSupplierLoading = true
      const filterUserId = this.wfData.filter(i => i.key === 'userId')
      downloadAuthFile({ fileName: this.imageList, userId: filterUserId.length ? filterUserId[0].value : '' })
        .then(response => {})
        .finally(() => {
          this.passSupplierLoading = false
        })
    }
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components && isShow" :key="pageCode" :schema="schema" @addApprovalForm="addApprovalForm" @toPendingDetail="toPendingDetail" @toViewDetail="toViewDetail" />
    <!-- 待审批列表 -->
    <el-drawer
      :title="(detailData.wfConfigName||'')+'（'+(detailData.wfStatus==='PROCESSING' ? '审批中' : '已完成')+'）'"
      :visible.sync="showOperationDetail"
      custom-class="approval-drawer"
      :before-close="closeDraw"
      direction="rtl"
    >
      <div>
        <el-form label-width="130px">
          <div v-for="(item, index) in wfData" :key="index" class="approval-detail">
            <template v-if="item.key === 'auditList'">
              <el-form-item v-for="(item1, index1) in item.list" :key="index1" class="half-line" :class="{ 'is-row': (index1 + 1) % 5 === 0 }" :label="getLabelValue(item)">
                {{ item1.value }}
              </el-form-item>
            </template>
            <el-form-item v-else :class="{ 'is-row': item.key === 'productList' }" :label="getLabelValue(item)">
              <span>{{ item.value }}</span>
            </el-form-item>
          </div>
          <el-button v-if="detailData.wfConfigId === 'R0006'" type="text" class="download-supplier" :loading="pendingSupplierLoading" @click="downloadPendingSupplier">供应商材料下载</el-button>
          <el-form-item label="事由:">
            <span>{{ detailData.wfDataName }}</span>
          </el-form-item>
          <el-form-item label="发起人:">
            <span>{{ detailData.role1No }}</span>
          </el-form-item>
        </el-form>
        <div v-for="(item, index) in detailData.newFlow" :key="index" class="audit-list">
          <div class="top">
            <div>
              <label class="label-style">{{ item.name }}:</label>
              {{ item.operator }}
            </div>
            <span> {{ item.status === 'PROCESSING' ? '处理中' : item.status === 'REVIEW_PASS' ? '通过' : item.status === 'REVIEW_REJECT' ? '拒绝' : '' }}</span>
          </div>
          <div v-if="index < detailData.newFlow.length - 1" class="bottom">
            <label class="label-style">留言:</label>
            <span>{{ item.comment }}</span>
          </div>
        </div>
        <el-form ref="ruleForm" :rules="rules" :model="ruleForm" label-width="130px" style="margin-top: 16px;">
          <el-form-item label="留言:" prop="remark">
            <el-input v-model="ruleForm.remark" maxlength="526" type="textarea" placeholder="请输入内容" />
          </el-form-item>
        </el-form>
        <div class="operation-btn">
          <a-button type="primary" :loading="passLoading" @click="pass">通过</a-button>
          <a-button type="danger" :loading="refuseLoading" @click="refuse">拒绝</a-button>
        </div>
      </div>
    </el-drawer>
    <!-- 全部审批列表 -->
    <el-drawer
      :title="(detailData.wfConfigName||'')+'（'+(detailData.wfStatus==='PROCESSING' ? '审批中' : '已完成')+'）'"
      :visible.sync="showViewDetail"
      custom-class="approval-drawer"
      direction="rtl"
    >
      <div>
        <el-form label-width="130px">
          <div v-for="(item, index) in wfData" :key="index" class="approval-detail">
            <template v-if="item.key === 'auditList'">
              <el-form-item v-for="(item1, index1) in item.list" :key="index1" class="half-line" :class="{ 'is-row': (index1 + 1) % 5 === 0 }" :label="getLabelValue(item)">
                {{ item1.value }}
              </el-form-item>
            </template>
            <el-form-item v-else :class="{ 'is-row': item.key === 'productList' }" :label="getLabelValue(item)">
              <span>{{ item.value }}</span>
            </el-form-item>
          </div>
          <el-button v-if="detailData.wfConfigId === 'R0006'" type="text" class="download-supplier" :loading="passSupplierLoading" @click="downloadPassSupplier">供应商材料下载</el-button>
          <el-form-item label="事由:">
            <span>{{ detailData.wfDataName }}</span>
          </el-form-item>
          <el-form-item label="发起人:">
            <span>{{ detailData.role1No }}</span>
          </el-form-item>
        </el-form>
        <div v-for="(item, index) in detailData.newFlow" :key="index" class="audit-list">
          <div class="top">
            <div>
              <label class="label-style">{{ item.name }}:</label>
              {{ item.operator }}
            </div>
            <span> {{ item.status === 'PROCESSING' ? '处理中' : item.status === 'REVIEW_PASS' ? '通过' : item.status === 'REVIEW_REJECT' ? '拒绝' : '' }}</span>
          </div>
          <div class="bottom">
            <label class="label-style">留言:</label>
            <span>{{ item.comment }}</span>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<style lang="scss" scoped>
::v-deep .approval-drawer {
    width: 530px !important;
    .el-drawer__header {
        margin-bottom: 20px;
    }
    .el-drawer__body {
        padding: 0 12px;
        .el-form-item {
            margin-bottom: 0;
        }
    }
    .audit-list {
        margin-top: 8px;
        padding-right: 24px;
        &:last-child {
            margin-bottom: 12px;
        }
        .top {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        .bottom {
            font-size: 12px;
        }
        .label-style {
            display: inline-block;
            width: 130px;
            text-align: right;
            padding-right: 12px;
        }
        .el-textarea {
            flex: 1;
        }
    }
    .approval-detail {
        display: flex;
        flex-wrap: wrap;
        .half-line {
            width: 49%;
            &.is-row {
                width: 100%;
            }
        }
    }
    .operation-btn {
        text-align: center;
        margin: 20px 0;
        button {
            margin-right: 12px;
        }
    }
    .download-supplier {
        margin-left: 130px;
    }
}
</style>
