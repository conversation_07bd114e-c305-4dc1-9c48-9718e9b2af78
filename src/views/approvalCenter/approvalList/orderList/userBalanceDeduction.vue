<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      size="small"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="事由" prop="reason">
        <el-input
          v-model.trim="ruleForm.reason"
          placeholder="请输入事由"
          maxlength="100"
          clearable
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model.trim="ruleForm.userId"
          placeholder="请输入用户ID"
          clearable
          @blur="blurUserId"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="">
        <el-input v-model.trim="userName" disabled />
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input
          v-model.trim="ruleForm.amount"
          placeholder="请输入金额"
          clearable
        />
      </el-form-item>
      <el-form-item label="币种" prop="currency">
        <el-select
          v-model="ruleForm.currency"
          placeholder="请选择币种"
          clearable
        >
          <el-option
            v-for="item in currencyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="扣减通道" prop="accountChannel">
        <el-select
          v-model="ruleForm.accountChannel"
          placeholder="请选择扣减通道"
          clearable
        >
          <el-option
            v-for="item in accountChannelList"
            :key="item.channelCode"
            :label="item.channelCode"
            :value="item.channelCode"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="operation-btn">
      <el-button type="primary" :loading="loading" @click="submit"
        >创建</el-button
      >
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script>
import {
  userDetail,
  apply,
  getChannelEffect,
} from "@/api/approvalCenter/approvalList";
export default {
  name: "UserBalanceDeduction",
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      userName: "",
      ruleForm: {
        reason: "",
        userId: "",
        amount: "",
        currency: "",
        accountChannel: "",
      },
      currencyList: [],
      accountChannelList: [],
      // 表单校验
      rules: {
        reason: [{ required: true, message: "请输入事由", trigger: "blur" }],
        userId: [{ required: true, message: "请输入用户ID", trigger: "blur" }],
        amount: [
          { required: true, message: "请输入金额", trigger: "blur" },
          {
            pattern:
              /^(?!0(\.0{1,2})?$)([1-9]\d*|0?\.\d{1,2}|[1-9]\d*\.\d{1,2})$/,
            message: "金额格式错误",
            trigger: "blur",
          },
        ],
        currency: [
          { required: true, message: "请选择币种", trigger: "change" },
        ],
        accountChannel: [
          { required: true, message: "请选择扣减通道", trigger: "change" },
        ],
      },
    };
  },
  created() {
    this.currencyList = JSON.parse(localStorage.getItem("kun_dicts")).filter(
      (item) => {
        return item.dictType === "kun_currency";
      }
    )[0].items;

    getChannelEffect({
      channelCurrencyType: "",
      isSupVa: "",
      isSupDeposit: "",
    }).then((res) => {
      const data = res.data.data?.channelInfoModelList;
      this.accountChannelList = data;
    });
  },
  methods: {
    blurUserId() {
      if (!this.ruleForm.userId) {
        this.userName = "";
        return;
      }
      userDetail({ userId: this.ruleForm.userId }).then((response) => {
        this.userName = response.data?.data?.companyEnName;
      });
    },
    // 关闭按钮
    handleClose() {
      this.$tab.closeOpenPage({ path: "/approvalCenter/approvalList/add" });
      this.$router.push("/approvalCenter/approvalList?refresh=true");
    },
    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.loading = true;
          apply({
            wfConfigId: this.type,
            wfDataName: this.ruleForm.reason,
            data: JSON.stringify([
              {
                label: "用户ID",
                key: "userId",
                value: this.ruleForm.userId,
              },
              {
                label: "客户名称",
                key: "userName",
                value: this.userName,
              },
              {
                label: "金额",
                key: "amount",
                value: this.ruleForm.amount,
              },
              {
                label: "币种",
                key: "currency",
                value: this.ruleForm.currency,
              },
              {
                label: "扣减通道",
                key: "accountChannel",
                value: this.ruleForm.accountChannel,
              },
            ]),
          })
            .then((response) => {
              if (response.data?.code === "AS000000") {
                this.$modal.msgSuccess("创建成功");
                this.handleClose();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.operation-btn {
  text-align: center;
  margin-top: 20px;
}
</style>
