<template>
  <div>
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" size="small" :inline="true" label-width="110px">
      <el-form-item label="事由" prop="reason">
        <el-input v-model.trim="ruleForm.reason" placeholder="请输入事由" maxlength="100" clearable />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model.trim="ruleForm.userId" placeholder="请输入用户ID" clearable @blur="blurUserId" />
      </el-form-item>
      <el-form-item label="客户名称" prop="">
        <el-input v-model.trim="userName" disabled />
      </el-form-item>
      <el-form-item label="GAS币种" prop="currency">
        <el-select v-model="ruleForm.currency" placeholder="请选择GAS币种" clearable @change="changeCurrency">
          <el-option
            v-for="item in currencyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="ruleForm.currency" label="GAS数量" prop="amount">
        <el-select v-model="ruleForm.amount" placeholder="请选择GAS数量" clearable>
          <el-option
            v-for="item in amountList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="充值账号" prop="channelAccountNo">
        <el-select v-model="ruleForm.channelAccountNo" placeholder="请选择充值账号" clearable>
          <el-option
            v-for="(item, key) in accountNoList"
            :key="key"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="operation-btn">
      <el-button type="primary" :loading="loading" @click="submit">创建</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { userDetail, apply, accountNoList } from '@/api/approvalCenter/approvalList'
export default {
  name: 'CAccessAuditOrder',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      userName: '',
      ruleForm: {
        reason: '',
        userId: '',
        currency: '',
        amount: '',
        channelAccountNo: ''
      },
      currencyList: [],
      ethList: [],
      trxList: [],
      accountNoList: [],
      // 表单校验
      rules: {
        reason: [
          { required: true, message: '请输入事由', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '请输入用户ID', trigger: 'blur' }
        ],
        currency: [
          { required: true, message: '请选择GAS币种', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请选择GAS数量', trigger: 'change' }
        ],
        channelAccountNo: [
          { required: true, message: '请输入充值账号', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    amountList: function() {
      if (this.ruleForm.currency === 'ETH') {
        return this.ethList
      } else if (this.ruleForm.currency === 'TRX') {
        return this.trxList
      } else {
        return []
      }
    }
  },
  created() {
    this.currencyList = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'gas_currency'
    })[0].items
    this.ethList = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'eth_gas_fee'
    })[0].items
    this.trxList = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'trx_gas_fee'
    })[0].items
  },
  methods: {
    getAccountNoList() {
      if (!this.userName || !this.ruleForm.currency) {
        return
      }
      const currencyObj = {
        TRX: 'TRX_TRC20',
        ETH: 'ETH_ERC20'
      }
      this.ruleForm.channelAccountNo = ''
      accountNoList({
        customerNo: this.ruleForm.userId,
        accountType: 'INNER_GAS_ACCOUNT',
        currency: this.ruleForm.currency,
        accountChannel: currencyObj[this.ruleForm.currency]
      }).then(response => {
        this.accountNoList = response.data?.data?.map(v => v.channelAccountNo)
      })
    },
    blurUserId() {
      if (!this.ruleForm.userId) {
        this.userName = ''
        return
      }
      userDetail({ userId: this.ruleForm.userId }).then(response => {
        this.userName = response.data?.data?.companyEnName
        this.getAccountNoList()
      })
    },
    changeCurrency() {
      this.ruleForm.amount = ''
      this.getAccountNoList()
    },
    // 关闭按钮
    handleClose() {
      this.$tab.closeOpenPage({ path: '/approvalCenter/approvalList/add' })
      this.$router.push('/approvalCenter/approvalList?refresh=true')
    },
    submit() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.loading = true
          apply({
            wfConfigId: this.type,
            wfDataName: this.ruleForm.reason,
            data: JSON.stringify([
              {
                label: '用户ID',
                key: 'userId',
                value: this.ruleForm.userId
              },
              {
                label: '客户名称',
                key: 'userName',
                value: this.userName
              },
              {
                label: 'GAS币种',
                key: 'currency',
                value: this.ruleForm.currency
              },
              {
                label: 'GAS数量',
                key: 'amount',
                value: this.ruleForm.amount
              },
              {
                label: '充值账号',
                key: 'channelAccountNo',
                value: this.ruleForm.channelAccountNo
              }
            ])
          }).then(response => {
            if (response.data?.code === 'AS000000') {
              this.$modal.msgSuccess('创建成功')
              this.handleClose()
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.operation-btn {
    text-align: center;
    margin-top: 20px;
}
</style>
