<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      size="small"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="事由" prop="reason">
        <el-input
          v-model.trim="ruleForm.reason"
          placeholder="请输入事由"
          maxlength="100"
          clearable
        />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model.trim="ruleForm.userId"
          placeholder="请输入用户ID"
          clearable
          @blur="blurUserId"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="">
        <el-input v-model.trim="userName" disabled />
      </el-form-item>
      <el-form-item label="有效期(天)" prop="validDate">
        <el-select
          v-model="ruleForm.validDate"
          placeholder="请选择有效期"
          clearable
        >
          <el-option
            v-for="(item, index) in validDateList"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="operation-btn">
      <el-button type="primary" :loading="loading" @click="submit"
        >创建</el-button
      >
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { userDetail, apply } from "@/api/approvalCenter/approvalList";
export default {
  name: "CAccessAuditOrder",
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      userName: "",
      ruleForm: {
        reason: "",
        userId: "",
        validDate: "",
      },
      validDateList: [7, 30, 60, 90, 180, 360],
      // 表单校验
      rules: {
        reason: [{ required: true, message: "请输入事由", trigger: "blur" }],
        userId: [{ required: true, message: "请输入用户ID", trigger: "blur" }],
        validDate: [
          { required: true, message: "请选择有效期", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    blurUserId() {
      if (!this.ruleForm.userId) {
        this.userName = "";
        return;
      }
      userDetail({ userId: this.ruleForm.userId }).then((response) => {
        this.userName = response.data?.data?.companyEnName;
      });
    },
    // 关闭按钮
    handleClose() {
      this.$tab.closeOpenPage({ path: "/approvalCenter/approvalList/add" });
      this.$router.push("/approvalCenter/approvalList?refresh=true");
    },
    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.loading = true;
          apply({
            wfConfigId: this.type,
            wfDataName: this.ruleForm.reason,
            data: JSON.stringify([
              {
                label: "用户ID",
                key: "userId",
                value: this.ruleForm.userId,
              },
              {
                label: "客户名称",
                key: "userName",
                value: this.userName,
              },
              {
                label: "有效期(天)",
                key: "validDate",
                value: this.ruleForm.validDate,
              },
            ]),
          })
            .then((response) => {
              if (response.data?.code === "AS000000") {
                this.$modal.msgSuccess("创建成功");
                this.handleClose();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.operation-btn {
  text-align: center;
  margin-top: 20px;
}
</style>
