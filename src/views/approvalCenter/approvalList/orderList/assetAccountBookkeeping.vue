<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      size="small"
      :inline="true"
      label-width="110px"
      class="asset-account-bookkeeping"
    >
      <el-form-item label="事由" prop="reason">
        <el-input v-model.trim="ruleForm.reason" placeholder="请输入事由" maxlength="100" clearable />
      </el-form-item>
      <el-form-item label="用户ID" prop="userId">
        <el-input v-model.trim="ruleForm.userId" placeholder="请输入用户ID" clearable @blur="blurUserId" />
      </el-form-item>
      <el-form-item label="客户名称" prop="">
        <el-input v-model.trim="userName" disabled />
      </el-form-item>
      <div style="width: 100%; margin-top: 20px;">
        <el-table
          :data="ruleForm.tableData"
          style="width: 100%;"
          :header-cell-style="{ 'background-color': '#fafafa' }"
        >
          <el-table-column label="金额">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.amount'" :rules="rules.amount">
                <el-input v-model="scope.row.amount" placeholder="请输入" clearable />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="币种">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.currency'" :rules="rules.currency">
                <el-select v-model="scope.row.currency" placeholder="请选择币种" clearable @change="accountSubjectChangeFn(scope.row, scope.$index)">
                  <el-option
                    v-for="item in currencyList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="方向">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.direction'" :rules="rules.direction">
                <el-select v-model="scope.row.direction" placeholder="请选择方向" clearable>
                  <el-option
                    v-for="item in directionList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="通道">
            <template slot-scope="scope">
              <el-form-item
                :prop="'tableData.' + scope.$index + '.accountChannel'"
                :rules="rules.accountChannel"
              >
                <el-select v-model="scope.row.accountChannel" placeholder="请通道" clearable @change="accountSubjectChangeFn(scope.row, scope.$index)">
                  <el-option
                    v-for="item in accountChannelList"
                    :key="item.channelCode"
                    :label="item.channelCode"
                    :value="item.channelCode"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="账户主题">
            <template slot-scope="scope">
              <el-form-item
                :prop="'tableData.' + scope.$index + '.accountSubject'"
                :rules="rules.accountSubject"
              >
                <el-select v-model="scope.row.accountSubject" placeholder="请选择" clearable @change="accountSubjectChangeFn(scope.row, scope.$index)">
                  <el-option
                    v-for="item in accountSubjectList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="充值账号">
            <template slot-scope="scope">
              <el-form-item
                :ref="'channelAccountNo' + scope.$index"
                :prop="'tableData.' + scope.$index + '.channelAccountNo'"
                :rules="[{
                  validator: (rule, value, callback) => {
                    if (value === '' && scope.row.accountNoList.length > 0 && scope.row.accountSubject==='INNER_ASSET_ACCOUNT' && digitalList.includes(scope.row.currency) && digitalChannelList.includes(scope.row.accountChannel)) {
                      callback(errorBind('请输入充值账号'));
                    } else {
                      callback();
                    }
                  }, trigger: 'change' }]"
              >
                <el-select v-model="scope.row.channelAccountNo" placeholder="请选择充值账号" clearable :disabled="!(scope.row.accountSubject==='INNER_ASSET_ACCOUNT' && digitalList.includes(scope.row.currency) && digitalChannelList.includes(scope.row.accountChannel) && userName)">
                  <el-option
                    v-for="(item, key) in scope.row.accountNoList || []"
                    :key="key"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <span v-if="ruleForm.tableData.length > 1" class="delete-btn" @click="deleteLine(scope.$index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="footer" @click="addLine">
          <i class="el-icon-plus" />
          新增
        </div>
      </div>
    </el-form>
    <div class="operation-btn">
      <el-button type="primary" :loading="loading" @click="submit">创建</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { userDetail, apply, accountNoList, getChannelEffect } from '@/api/approvalCenter/approvalList'
export default {
  name: 'AssetAccountBookkeeping',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      userName: '',
      ruleForm: {
        reason: '',
        userId: '',
        tableData: [
          {
            amount: '',
            currency: '',
            direction: '',
            accountChannel: '',
            accountSubject: '',
            channelAccountNo: '',
            accountNoList: []
          }
        ]
      },
      currencyList: [],
      accountChannelList: [],
      directionList: [],
      accountSubjectList: [],
      // 表单校验
      rules: {
        reason: [
          { required: true, message: '请输入事由', trigger: 'blur' }
        ],
        userId: [
          { required: true, message: '请输入用户ID', trigger: 'blur' }
        ],
        amount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          {
            pattern: /^(?!0(\.0{1,2})?$)([1-9]\d*|0?\.\d{1,2}|[1-9]\d*\.\d{1,2})$/,
            message: '金额格式错误',
            trigger: 'blur'
          }
        ],
        currency: [
          { required: true, message: '请选择币种', trigger: 'change' }
        ],
        direction: [
          { required: true, message: '请选择方向', trigger: 'change' }
        ],
        accountChannel: [
          { required: true, message: '请选择通道', trigger: 'change' }
        ],
        accountSubject: [
          { required: true, message: '请选择账户主题', trigger: 'change' }
        ],
        channelAccountNo: [
          { required: true, message: '请输入充值账号', trigger: 'blur' }
        ]
      },
      digitalList: ['USDT', 'USDC'],
      digitalChannelList: ['ETH_ERC20', 'TRX_TRC20']
    }
  },
  created() {
    this.currencyList = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'kun_currency'
    })[0].items
    this.directionList = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'account_direction'
    })[0].items
    this.accountSubjectList = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'workflow_account_sujest'
    })[0].items

    getChannelEffect({
      channelCurrencyType: "",
      isSupVa: "YES",
      isSupDeposit: "",
      channelCategoryCode: 'ONLINE',
    }).then((res) => {
      const data = res.data.data?.channelInfoModelList
      this.accountChannelList = data
    });
  },
  methods: {
    getAccountNoList(row) {
      if (!this.userName || !this.digitalList.includes(row.currency) || !this.digitalChannelList.includes(row.accountChannel) || row.accountSubject !== 'INNER_ASSET_ACCOUNT') {
        return
      }
      accountNoList({
        customerNo: this.ruleForm.userId,
        accountType: 'INNER_ASSET_ACCOUNT',
        currency: row.currency,
        accountChannel: row.accountChannel
      }).then(response => {
        row.channelAccountNo = '';
        row.accountNoList = response.data?.data.map(v => v.channelAccountNo)
      })
    },
    accountSubjectChangeFn(row, index) {
      if (!(row.accountSubject === 'INNER_ASSET_ACCOUNT' && this.digitalList.includes(row.currency) && this.digitalChannelList.includes(row.accountChannel))) {
        row.channelAccountNo = ''
        this.$refs[`channelAccountNo${index}`].resetField()
      }
      this.getAccountNoList(row)
    },
    errorBind(str) {
      return new Error(str)
    },
    checkUniqueFields(arr, field) {
      const combined = new Set()
      return arr.every(obj => {
        const key = obj[field]
        if (combined.has(key)) {
          return false
        }
        combined.add(key)
        return true
      })
    },
    blurUserId() {
      if (!this.ruleForm.userId) {
        this.userName = ''
        return
      }
      userDetail({ userId: this.ruleForm.userId }).then(response => {
        this.userName = response.data?.data?.companyEnName
        this.ruleForm.tableData.forEach(row => this.getAccountNoList(row))
      })
    },
    // 关闭按钮
    handleClose() {
      this.$tab.closeOpenPage({ path: '/approvalCenter/approvalList/add' })
      this.$router.push('/approvalCenter/approvalList?refresh=true')
    },
    addLine() {
      this.ruleForm.tableData.push({
        amount: '',
        currency: '',
        direction: '',
        accountChannel: '',
        accountSubject: '',
        channelAccountNo: '',
        accountNoList: []
      })
    },
    deleteLine(index) {
      this.ruleForm.tableData.splice(index, 1)
    },
    submit() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          if (!this.ruleForm.tableData.some(i => {
            return i.accountSubject === 'INNER_ASSET_ACCOUNT'
          })) {
            this.$modal.msgError('至少有一项的账户主题是客户散列账户')
            return
          }
          if (!this.ruleForm.tableData.some(i => {
            return i.accountSubject === 'ASSET'
          })) {
            this.$modal.msgError('至少有一项的账户主题是内部资产大账户')
            return
          }
          if (!this.checkUniqueFields(this.ruleForm.tableData, 'accountSubject')) {
            this.$modal.msgError('账户主题不能重复')
            return
          }
          const auditList = this.ruleForm.tableData.map(i => {
            return {
              'amount_金额': i.amount,
              'currency_币种': i.currency,
              'direction_方向': i.direction,
              'accountChannel_通道': i.accountChannel,
              'accountSubject_账户主题': i.accountSubject,
              'channelAccountNo_充值账号': i.channelAccountNo
            }
          })
          const obj = {
            wfConfigId: this.type,
            wfDataName: this.ruleForm.reason,
            data: JSON.stringify([
              {
                label: '用户ID',
                key: 'userId',
                value: this.ruleForm.userId
              },
              {
                label: '客户名称',
                key: 'userName',
                value: this.userName
              },
              {
                label: '审核项',
                key: 'auditList',
                value: auditList
              }
            ])
          }
          this.loading = true
          apply(obj).then(response => {
            if (response.data?.code === 'AS000000') {
              this.$modal.msgSuccess('创建成功')
              this.handleClose()
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep .asset-account-bookkeeping {
    .el-form-item {
        width: 100%;

        .el-form-item__content {
            width: 100%;

            &>.el-input,
            &>.el-select {
                width: 90%;
            }
        }
    }

    .delete-btn {
        display: inline-block;
        cursor: pointer;
        margin-bottom: 18px;
    }

    .footer {
        color: rgba(0, 0, 0, .65);
        border: 1px dashed #efefef;
        padding: 4px;
        margin: 8px 0;
        text-align: center;
        cursor: pointer;

        &:hover {
            border: 1px dashed #1890ff;
            color: #1890ff;
        }
    }
}

.operation-btn {
    text-align: center;
    margin-top: 20px;
}
</style>
