<template>
  <div>
    <el-form ref="ruleForm" :model="ruleForm" :rules="rules" size="small" :inline="true" label-width="110px">
      <el-form-item label="事由" prop="reason">
        <el-input v-model.trim="ruleForm.reason" placeholder="请输入事由" maxlength="100" clearable />
      </el-form-item>
      <el-form-item label="客户英文名称" prop="customerEnName">
        <el-input v-model.trim="ruleForm.customerEnName" placeholder="请输入客户英文名称" clearable />
      </el-form-item>
      <el-form-item label="注册邮箱" prop="email">
        <el-input v-model.trim="ruleForm.email" placeholder="请输入注册邮箱" clearable />
      </el-form-item>
      <el-form-item label="产品列表" prop="productList" style="width: 100%;">
        <el-checkbox-group v-model="ruleForm.productList">
          <el-checkbox v-for="item in emailList" :key="item.code" :label="item.code" :disabled="disabledList.includes(item.code)">{{ item.name }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <div class="operation-btn">
      <el-button type="primary" :loading="loading" @click="submit">创建</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { apply } from '@/api/approvalCenter/approvalList'
export default {
  name: 'CAccessAuditOrder',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      ruleForm: {
        reason: '',
        customerEnName: '',
        email: '',
        productList: ['DIGITAL_R_W', 'LEGAL_R_W']
      },
      emailList: [],
      disabledList: ['DIGITAL_R_W', 'LEGAL_R_W'],
      // 表单校验
      rules: {
        reason: [
          { required: true, message: '请输入事由', trigger: 'blur' }
        ],
        customerEnName: [
          { required: true, message: '请输入客户英文名称', trigger: 'blur' },
          {
            pattern: /^[^\u4e00-\u9fff]+$/,
            message: '不能输入中文',
            trigger: 'blur'
          }
        ],
        email: [
          { required: true, message: '请输入注册邮箱', trigger: 'blur' }
        ],
        productList: [
          { required: true, message: '请选择产品', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.emailList = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'dac_product'
    })[0].items
  },
  methods: {
    // 关闭按钮
    handleClose() {
      this.$tab.closeOpenPage({ path: '/approvalCenter/approvalList/add' })
      this.$router.push('/approvalCenter/approvalList?refresh=true')
    },
    submit() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          this.loading = true
          apply({
            wfConfigId: this.type,
            wfDataName: this.ruleForm.reason,
            data: JSON.stringify([
              {
                label: '客户英文名称',
                key: 'customerEnName',
                value: this.ruleForm.customerEnName
              }, {
                label: '注册邮箱',
                key: 'email',
                value: this.ruleForm.email
              }, {
                label: '产品列表',
                key: 'productList',
                value: this.ruleForm.productList.join(',')
              }
            ])
          }).then(response => {
            if (response.data?.code === 'AS000000') {
              this.$modal.msgSuccess('创建成功')
              this.handleClose()
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.operation-btn {
    text-align: center;
    margin-top: 20px;
}
</style>
