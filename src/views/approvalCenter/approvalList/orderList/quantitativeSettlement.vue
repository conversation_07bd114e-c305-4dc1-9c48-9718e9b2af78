<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      size="small"
      :inline="true"
      label-width="110px"
    >
      <el-form-item label="结算账户ID" prop="userId">
        <!--        <el-input v-model.trim="ruleForm.userId" placeholder="请输入结算账户ID" clearable @blur="blurUserId" />-->
        <el-select
          v-model="ruleForm.userId"
          filterable
          placeholder="请选择结算账户ID"
          clearable
        >
          <el-option
            v-for="item in noList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="事由" prop="reason">
        <el-input
          v-model.trim="ruleForm.reason"
          placeholder="请输入事由"
          maxlength="100"
          clearable
        />
      </el-form-item>
      <el-form-item label="结算扣减通道" prop="withdrawChannel">
        <el-select
          v-model="ruleForm.withdrawChannel"
          placeholder="请选择结算扣减通道"
          clearable
        >
          <el-option
            v-for="item in channelList"
            :key="item.channelCode"
            :label="item.channelCode"
            :value="item.channelCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="结算上账通道" prop="depositChannel">
        <el-select
          v-model="ruleForm.depositChannel"
          placeholder="请选择结算上账通道"
          clearable
        >
          <el-option
            v-for="item in channelList"
            :key="item.channelCode"
            :label="item.channelCode"
            :value="item.channelCode"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="转出币种" prop="withdrawCurrency">
        <el-select
          v-model="ruleForm.withdrawCurrency"
          placeholder="请选择转出币种"
          clearable
        >
          <el-option
            v-for="item in currencyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="转入币种" prop="depositCurrency">
        <el-select
          v-model="ruleForm.depositCurrency"
          placeholder="请选择转入币种"
          clearable
        >
          <el-option
            v-for="item in currencyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="转出数量" prop="withdrawAmount">
        <el-input
          v-model.trim="ruleForm.withdrawAmount"
          placeholder="请输入转出数量"
        />
      </el-form-item>
      <el-form-item label="转入数量" prop="depositAmount">
        <el-input
          v-model.trim="ruleForm.depositAmount"
          placeholder="请输入转入数量"
        />
      </el-form-item>
    </el-form>
    <div class="operation-btn">
      <el-button type="primary" :loading="loading" @click="submit"
        >创建</el-button
      >
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script>
import {
  userDetail,
  apply,
  getCustomerNoList,
  getChannelEffect,
} from "@/api/approvalCenter/approvalList";
export default {
  name: "CAccessAuditOrder",
  props: {
    type: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      userName: "",
      labels: {
        userId: "结算账户id",
        reason: "事由",
        withdrawChannel: "结算扣减通道",
        withdrawCurrency: "转出币种",
        withdrawAmount: "转出数量",
        depositChannel: "结算上账通道",
        depositCurrency: "转入币种",
        depositAmount: "转入数量",
      },
      ruleForm: {
        userId: "",
        reason: "",
        withdrawChannel: "",
        withdrawCurrency: "",
        withdrawAmount: "",
        depositChannel: "",
        depositCurrency: "",
        depositAmount: "",
      },
      noList: [],
      currencyList: [],
      channelList: [],
      // 表单校验
      rules: {
        reason: [{ required: true, message: "请输入事由", trigger: "blur" }],
        userId: [
          { required: true, message: "请选择结算账户ID", trigger: "change" },
        ],
        withdrawChannel: [
          { required: true, message: "请选择结算扣减通道", trigger: "change" },
        ],
        withdrawCurrency: [
          { required: true, message: "请选择转出币种", trigger: "change" },
        ],
        withdrawAmount: [
          { required: true, message: "请输入转出数量", trigger: "blur" },
          {
            pattern:
              /^(?!0\.0{1,8}$)(0|[1-9]\d*|0?\.\d{1,8}|[1-9]\d*\.\d{1,8})$/,
            message: "转出数量格式错误",
            trigger: "blur",
          },
        ],
        depositChannel: [
          { required: true, message: "请选择结算上账通道", trigger: "change" },
        ],
        depositCurrency: [
          { required: true, message: "请选择转入币种", trigger: "change" },
        ],
        depositAmount: [
          { required: true, message: "请输入转入数量", trigger: "blur" },
          {
            pattern:
              /^(?!0\.0{1,8}$)(0|[1-9]\d*|0?\.\d{1,8}|[1-9]\d*\.\d{1,8})$/,
            message: "转入数量格式错误",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    // getCustomerNoList().then(res => {
    //   this.noList = res.data.data
    // })

    this.noList = JSON.parse(localStorage.getItem("kun_dicts")).filter(
      (item) => {
        return item.dictType === "quant_customer_no";
      }
    )[0].items;
    this.currencyList = JSON.parse(localStorage.getItem("kun_dicts"))
      .filter((item) => {
        return item.dictType === "kun_currency";
      })[0]
      .items.filter((v) => ["USDT", "USD"].includes(v.code));
    this.channelList = JSON.parse(localStorage.getItem("kun_dicts")).filter(
      (item) => {
        return item.dictType === "asset_channel_code";
      }
    )[0].items;
    getChannelEffect({
      channelCurrencyType: "",
      isSupVa: "",
      isSupDeposit: "",
    }).then((res) => {
      const data = res.data.data?.channelInfoModelList;
      this.channelList = data;
    });
  },
  methods: {
    // 关闭按钮
    handleClose() {
      this.$tab.closeOpenPage({ path: "/approvalCenter/approvalList/add" });
      this.$router.push("/approvalCenter/approvalList?refresh=true");
    },
    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.loading = true;
          apply({
            wfConfigId: this.type,
            wfDataName: this.ruleForm.reason,
            data: JSON.stringify(
              Object.keys(this.ruleForm)
                .filter((key) => key !== "reason")
                .map((key) => ({
                  label: this.labels[key],
                  key: key,
                  value: this.ruleForm[key],
                }))
            ),
          })
            .then((response) => {
              if (response.data?.code === "AS000000") {
                this.$modal.msgSuccess("创建成功");
                this.handleClose();
              }
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
  },
};
</script>
<style scoped lang="scss">
.operation-btn {
  text-align: center;
  margin-top: 20px;
}
</style>
