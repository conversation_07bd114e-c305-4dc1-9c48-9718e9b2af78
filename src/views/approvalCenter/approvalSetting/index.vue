<template>
  <div class="approval-setting">
    <el-form ref="ruleForm" :model="ruleForm" size="small" :inline="true" label-width="110px">
      <el-form-item label="类型" prop="type">
        <el-select v-model="ruleForm.type" placeholder="请选择类型" clearable @change="queryRole">
          <el-option
            v-for="item in configDataList"
            :key="item.wfConfigId"
            :label="item.wfConfigName"
            :value="item.wfConfigId"
          />
        </el-select>
      </el-form-item>
      <div v-for="(item, index) in auditList" :key="index" class="single">
        <div class="title">{{ item.name }}</div>
        <el-table
          :data="renderDataResource(item.roleId)"
          style="width: 100%;"
          :header-cell-style="{ 'background-color': '#fafafa' }"
        >
          <el-table-column label="运营后台账户">
            <template slot-scope="scope">
              <el-form-item
                :prop="'role' + item.roleId + 'Nos.' + scope.$index + '.operateNo'"
                :rules="rules.operateNo"
              >
                <el-input v-model="scope.row.operateNo" placeholder="请输入账号" clearable />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="LarkID">
            <template slot-scope="scope">
              <el-form-item :prop="'role' + item.roleId + 'Nos.' + scope.$index + '.larkId'" :rules="rules.larkId">
                <el-input v-model="scope.row.larkId" placeholder="请输入LarkID" clearable />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <span class="delete-btn" @click="roleDeleteLine(item.roleId, scope.$index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
        <div class="footer" @click="roleAddLine(item.roleId)">
          <i class="el-icon-plus" />
          新增
        </div>
      </div>
    </el-form>
    <div class="operation-btn">
      <el-button type="primary" :loading="loading" @click="submit">保存</el-button>
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { configList, roleConfigQuery, roleConfigSave } from '@/api/approvalCenter/approvalList'
export default {
  name: 'ApprovalSetting',
  data() {
    return {
      configDataList: [],
      loading: false,
      ruleForm: {
        type: '',
        role1Nos: [],
        role2Nos: [],
        role3Nos: [],
        role4Nos: [],
        role5Nos: []
      },
      auditList: [
        {
          name: '发起人',
          roleId: '1'
        },
        {
          name: '一级审批',
          roleId: '2'
        },
        {
          name: '二级审批',
          roleId: '3'
        },
        {
          name: '三级审批',
          roleId: '4'
        },
        {
          name: '四级审批',
          roleId: '5'
        }
      ],
      // 表单校验
      rules: {
        operateNo: [
          { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        larkId: [
          { required: true, message: '请输入LarkID', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    configList({ onlyShow: false }).then(response => {
      this.configDataList = response.data?.data
      this.ruleForm.type = response.data?.data[0]?.wfConfigId
      this.queryRole()
    })
  },
  methods: {
    renderDataResource(roleId) {
      return this.ruleForm[`role${roleId}Nos`]
    },
    // 关闭按钮
    handleClose() {
      this.$tab.closeOpenPage({ path: '/approvalCenter/approvalSetting' })
      this.$router.push('/approvalCenter/approvalList?refresh=true')
    },
    queryRole() {
      roleConfigQuery({ wfConfigId: this.ruleForm.type }).then(response => {
        if (response.data?.code === 'AS000000' && response.data.data?.length) {
          const data = response.data.data[0]
          this.ruleForm.role1Nos = data.role1Nos || []
          this.ruleForm.role2Nos = data.role2Nos || []
          this.ruleForm.role3Nos = data.role3Nos || []
          this.ruleForm.role4Nos = data.role4Nos || []
          this.ruleForm.role5Nos = data.role5Nos || []
        }
      })
    },
    roleAddLine(id) {
      this.ruleForm[`role${id}Nos`].push({
        operateNo: '',
        larkId: ''
      })
    },
    roleDeleteLine(id, index) {
      this.ruleForm[`role${id}Nos`].splice(index, 1)
    },
    submit() {
      this.$refs['ruleForm'].validate(valid => {
        if (valid) {
          if (this.ruleForm.role1Nos.length === 0) {
            this.$modal.msgError('发起方至少填写一条')
            return
          }
          if (this.ruleForm.role2Nos.length === 0) {
            this.$modal.msgError('一级审批至少填写一条')
            return
          }
          this.loading = true
          roleConfigSave({
            wfConfigId: this.ruleForm.type,
            role1Nos: this.ruleForm.role1Nos,
            role2Nos: this.ruleForm.role2Nos,
            role3Nos: this.ruleForm.role3Nos,
            role4Nos: this.ruleForm.role4Nos,
            role5Nos: this.ruleForm.role5Nos
          }).then(response => {
            if (response.data?.code === 'AS000000') {
              this.$modal.msgSuccess('设置成功')
              this.handleClose()
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.approval-setting {
  padding: 20px;

  ::v-deep .el-form {
    display: flex;
    flex-wrap: wrap;

    &>.el-form-item {
      width: 49%;
      display: flex;

      &>.el-form-item__content {
        flex: 1;

        &>.el-input,
        &>.el-select {
          width: 90%;
        }
      }
    }
  }

  .title {
    font-weight: 500;
    margin-bottom: 8px;
  }

  .delete-btn {
    display: inline-block;
    cursor: pointer;
    margin-bottom: 18px;
  }

  .footer {
    color: rgba(0, 0, 0, .65);
    border: 1px dashed #efefef;
    padding: 4px;
    margin: 8px 0;
    text-align: center;
    cursor: pointer;

    &:hover {
      border: 1px dashed #1890ff;
      color: #1890ff;
    }
  }

  .operation-btn {
    text-align: center;
    margin-top: 20px;
  }

  .single {
    width: 100%;
    margin-bottom: 8px;
    padding: 0 70px;

    ::v-deep .el-form-item {
      width: 100%;

      .el-form-item__content {
        width: 100%;

        &>.el-input,
        &>.el-select {
          width: 90%;
        }
      }
    }
  }
}
</style>
