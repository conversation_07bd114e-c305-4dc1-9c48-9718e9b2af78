<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import { addPointsRule, updatePointsRule, getPointsRuleDetail } from '@/api/pointsManagement'
import dayjs from 'dayjs'

export default defineComponent({
  data() {
    return {
      pageCode: 'kun_pointsConfiguration_list',
      schema: {},
      dialogVisible: false,
      loading: false,
      viewStatus: '',
      ruleId: '',
      formData: {
        ruleName: '',
        ruleScene: undefined,
        ruleConfig: [
          {
            currency: undefined,
            items: [
              {
                name: '',
                minAmount: '',
                maxAmount: '',
                event: undefined,
                distributionType: undefined,
                distributionValue: ''
              }
            ]
          }
        ],
        startDate: '',
        endDate: '',
        cycleValue: '',
        cycleUnit: '年'
      },
      validityPeriod: {
        value: '',
        unit: '年'
      },
      invalidFields: {
        ruleName: false,
        ruleScene: false,
        dateRange: false,
        cycleValue: false,
        cycleUnit: false,
        ruleConfig: [] // [{currency: false, items: [{name: false, minAmount: false, ...}]}]
      },
      rewardTypeOptions: [
        // { value: 'RATIO', label: '比例' },
        // { value: 'FIXED', label: '固定' }
      ],
      currencyOptions: [
        { value: 'USDT', label: 'USDT' },
        { value: 'USDC', label: 'USDC' },
        { value: 'USD', label: 'USD' },
        { value: 'HKD', label: 'HKD' },
        { value: 'EUR', label: 'EUR' }
      ],
      ruleSceneOptions: [],
      constants: {
        DEFAULT_CURRENCY: 'USD',
        DEFAULT_UNIT: '年',
        UNIT_MAP: {
          '年': 'YEAR',
          'YEAR': '年'
        }
      },
      kunDicts: [],
      expireUnitOptions: [],
      defaultTimeRange: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')]
    }
  },
  computed: {
    dateRange() {
      const { startDate, endDate } = this.formData
      return (startDate && endDate) ? [startDate, endDate] : []
    },
    eventOptions() {
      if (!this.kunDicts.length) return []
      if (this.formData.ruleScene === 'EXCHANGE') {
        return this.kunDicts.find(item => item.dictType === 'kun_point_exchange_event')?.items || []
      }
      if (this.formData.ruleScene === 'BALANCE') {
        return this.kunDicts.find(item => item.dictType === 'kun_point_balance_event')?.items || []
      }
      return []
    },
    renderer() {
      return this[`__yeepay_lowcode_${this.pageCode}__`].value
    }
  },
  watch: {
    'formData.ruleScene'() {
      this.formData.ruleConfig.forEach(config => {
        config.items.forEach(item => {
          item.event = undefined
        })
      })
    },
    'formData.ruleConfig': {
      handler(newConfig) {
        newConfig.forEach(config => {
          config.items.forEach(item => {
            if (item.distributionType && item.distributionValue !== undefined && item.distributionValue !== null) {
              // 这里直接传 item.distributionValue
              this.handleDistributionValueInput(item.distributionValue, item)
            }
          })
        })
      },
      deep: true
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
    this.ruleSceneOptions = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'kun_point_rule_scene'
    })[0].items
    this.kunDicts = JSON.parse(localStorage.getItem('kun_dicts')) || []
    this.rewardTypeOptions = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'kun_point_distribute_type'
    })[0].items
    this.expireUnitOptions = JSON.parse(localStorage.getItem('kun_dicts')).filter(item => {
      return item.dictType === 'kun_point_expire_unit'
    })[0].items
  },
  methods: {
    // 路由处理，控制弹窗显示和表单初始化
    async handleRoute(viewStatus, ruleId) {
      // 设置当前操作状态（新增/修改）
      this.viewStatus = viewStatus
      if (this.viewStatus === 'add') {
        // 新增时重置表单并显示弹窗
        this.resetForm()
        this.dialogVisible = true
        return
      }

      if (this.viewStatus === 'fix' && ruleId) {
        try {
          this.loading = true
          this.ruleId = ruleId
          // 获取规则详情数据
          const { data } = await getPointsRuleDetail({ ruleId: this.ruleId })
          // 初始化表单数据
          this.setFormData(data.data)
          this.dialogVisible = true
        } catch (error) {
          console.error('获取规则详情失败:', error)
        } finally {
          this.loading = false
        }
      }
    },
    // 设置表单数据，兼容 ruleConfig 为字符串或对象
    setFormData(data) {
      // 赋值表单数据
      this.formData = { ...data }
      // 兼容 ruleConfig 为字符串的情况
      if (typeof this.formData.ruleConfig === 'string') {
        const parsed = JSON.parse(this.formData.ruleConfig)
        this.formData.ruleConfig = parsed.rules || []
      }
      // 兼容 ruleConfig 为对象且包含 rules 数组的情况
      if (typeof this.formData.ruleConfig === 'object' && this.formData.ruleConfig.rules) {
        this.formData.ruleConfig = this.formData.ruleConfig.rules
      }
      // 格式化日期
      if (this.formData.startDate) {
        this.formData.startDate = dayjs(this.formData.startDate).format('YYYY-MM-DD')
      }
      if (this.formData.endDate) {
        this.formData.endDate = dayjs(this.formData.endDate).format('YYYY-MM-DD')
      }
      // 处理周期单位
      if (this.formData.cycleUnit === 'YEAR') {
        this.formData.cycleUnit = this.constants.DEFAULT_UNIT
      }
    },
    // 重置表单为初始状态
    resetForm() {
      // 重置表单为初始状态
      this.formData = {
        ruleName: '',
        ruleScene: undefined,
        ruleConfig: [this.createEmptyCurrencyRule()],
        startDate: '',
        endDate: '',
        cycleValue: '',
        cycleUnit: this.constants.DEFAULT_UNIT
      }
      // 兼容 ruleConfig 为字符串的情况
      if (typeof this.formData.ruleConfig === 'string') {
        const parsed = JSON.parse(this.formData.ruleConfig)
        this.formData.ruleConfig = parsed.rules || []
      }
      // 重置校验状态
      this.invalidFields = {
        ruleName: false,
        ruleScene: false,
        dateRange: false,
        cycleValue: false,
        cycleUnit: false,
        ruleConfig: []
      }
    },
    // 创建一个空的币种规则
    createEmptyCurrencyRule() {
      // 创建一个空币种规则对象
      return {
        currency: undefined,
        items: [this.createEmptyRuleItem()]
      }
    },
    // 创建一个空的规则项
    createEmptyRuleItem() {
      // 创建一个空规则项对象
      return {
        name: '',
        minAmount: '',
        maxAmount: '',
        event: undefined,
        distributionType: undefined,
        distributionValue: ''
      }
    },
    // 添加币种规则
    addCurrencyRule() {
      // 添加一个币种规则
      this.formData.ruleConfig.push(this.createEmptyCurrencyRule())
    },
    // 添加规则项
    addRule(currencyRule) {
      // 添加一个规则项
      currencyRule.items.push(this.createEmptyRuleItem())
    },
    // 删除规则项
    removeRule(currencyRule, index) {
      // 删除指定规则项
      currencyRule.items.splice(index, 1)
    },
    // 删除币种规则
    removeCurrencyRule(index) {
      // 删除指定币种规则
      this.formData.ruleConfig.splice(index, 1)
    },
    // 校验表单，区分新增和修改
    validateForm() {
      // 校验表单，区分新增和修改
      let valid = true
      // 修改时只校验规则名称和生效时间
      if (this.viewStatus === 'fix') {
        const basicFields = {
          ruleName: !this.formData.ruleName,
          dateRange: !this.formData.startDate || !this.formData.endDate
        }
        Object.keys(basicFields).forEach(key => {
          this.invalidFields[key] = basicFields[key]
          if (basicFields[key]) valid = false
        })
        return valid
      }
      // 新增时校验所有字段
      const basicFields = {
        ruleName: !this.formData.ruleName,
        ruleScene: !this.formData.ruleScene,
        dateRange: !this.formData.startDate || !this.formData.endDate,
        cycleValue: !this.formData.cycleValue && this.formData.cycleUnit !== 'PERMANENT',
        cycleUnit: !this.formData.cycleUnit
      }
      Object.keys(basicFields).forEach(key => {
        this.invalidFields[key] = basicFields[key]
        if (basicFields[key]) valid = false
      })
      this.invalidFields.ruleConfig = this.formData.ruleConfig.map(config => {
        const currencyInvalid = !config.currency
        const itemsInvalid = config.items.map(item => {
          const itemInvalid = {
            name: !item.name,
            minAmount: !item.minAmount && item.minAmount !== 0,
            maxAmount: !item.maxAmount && item.maxAmount !== 0,
            event: !item.event,
            distributionType: !item.distributionType,
            distributionValue: !item.distributionValue && item.distributionValue !== 0
          }

          // 添加最大值必须大于最小值的验证
          if (item.minAmount && item.maxAmount) {
            const minAmount = parseInt(item.minAmount)
            const maxAmount = parseInt(item.maxAmount)
            if (maxAmount <= minAmount) {
              itemInvalid.maxAmount = true
              valid = false
            }
          }

          // 添加发放数量验证
          if (item.distributionType && item.distributionValue) {
            if (item.distributionType === 'RATIO') {
              // 比例类型：允许小数，但必须是正数
              const value = parseFloat(item.distributionValue)
              if (isNaN(value) || value <= 0) {
                itemInvalid.distributionValue = true
                valid = false
              }
            } else if (item.distributionType === 'FIXED') {
              // 固定类型：必须是正整数
              const value = parseFloat(item.distributionValue)
              if (!Number.isInteger(value) || value <= 0) {
                itemInvalid.distributionValue = true
                valid = false
              }
            }
          }

          if (Object.values(itemInvalid).some(Boolean)) valid = false
          return itemInvalid
        })
        if (currencyInvalid) valid = false
        return { currency: currencyInvalid, items: itemsInvalid }
      })
      return valid
    },
    // 提交表单，处理新增和修改
    async handleSubmit() {
      // 校验表单
      if (!this.validateForm()) {
        // 检查是否有最大值小于等于最小值的错误
        const hasRangeError = this.invalidFields.ruleConfig && this.invalidFields.ruleConfig.some(config =>
          config.items && config.items.some(item =>
            item.maxAmount && item.minAmount && parseInt(item.maxAmount) <= parseInt(item.minAmount)
          )
        )

        // 检查是否有发放数量验证错误
        const hasDistributionError = this.invalidFields.ruleConfig && this.invalidFields.ruleConfig.some(config => config.items && config.items.some(item => item.distributionValue && item.distributionType && ((item.distributionType === 'RATIO' && (isNaN(parseFloat(item.distributionValue)) || parseFloat(item.distributionValue) <= 0)) || (item.distributionType === 'FIXED' && (!Number.isInteger(parseFloat(item.distributionValue)) || parseFloat(item.distributionValue) <= 0)))))

        if (hasRangeError) {
          this.$message.error('最大值必须大于最小值')
        } else if (hasDistributionError) {
          this.$message.error('比例类型发放数量必须是正数，固定类型发放数量必须是正整数')
        } else {
          this.$message.error('未填写完整数据')
        }
        return
      }
      // 组装提交数据
      const submitData = this.prepareSubmitData()
      try {
        if (this.viewStatus === 'add') {
          await addPointsRule(submitData)
          this.$message.success('操作成功')
          this.renderer.runQuery('queryList')
        } else {
          await updatePointsRule(submitData)
          this.$message.success('操作成功')
          this.renderer.runQuery('queryList')
        }
      } catch (err) {
        console.log(err)
      } finally {
        this.dialogVisible = false
      }
    },
    // 组装提交数据，区分新增和修改
    prepareSubmitData() {
      // 组装提交数据，区分新增和修改
      const submitData = { ...this.formData }
      // 格式化日期
      submitData.startDate = this.formData.startDate ? dayjs(this.formData.startDate).format('YYYY-MM-DD HH:mm:ss') : ''
      submitData.endDate = this.formData.endDate ? dayjs(this.formData.endDate).format('YYYY-MM-DD HH:mm:ss') : ''
      // 修改时只提交部分字段
      if (this.viewStatus === 'fix') {
        return {
          ruleName: submitData.ruleName,
          startDate: submitData.startDate,
          endDate: submitData.endDate,
          ruleId: this.ruleId
        }
      }
      // 新增时提交全部字段，临时将 ruleConfig 转换为字符串格式
      submitData.cycleUnit = this.constants.UNIT_MAP[this.formData.cycleUnit] || this.formData.cycleUnit
      submitData.ruleConfig = {
        rules: this.formData.ruleConfig
      }
      submitData.requestNo = Date.now()
      return submitData
    },
    // 处理生效时间范围选择
    handleDateRangeChange(dates) {
      // 处理生效时间范围选择
      if (dates && dates.length === 2) {
        // 使用 dayjs 处理，保证有时分秒
        const start = dayjs(dates[0]).format('YYYY-MM-DD') + ' 00:00:00'
        const end = dayjs(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
        this.formData.startDate = start
        this.formData.endDate = end
      } else {
        this.formData.startDate = ''
        this.formData.endDate = ''
      }
    },
    // 判断币种是否已被其他规则选中（当前编辑项除外）
    isCurrencySelected(currency, currentIndex) {
      // 判断币种是否已被其他规则选中（当前编辑项除外）
      return this.formData.ruleConfig.some((config, idx) => config.currency === currency && idx !== currentIndex)
    },
    handleCycleUnitChange() {
      if (this.formData.cycleUnit === 'PERMANENT') {
        this.formData.cycleValue = ''
      }
    },
    handleIntegerInput(event, item, field) {
      // Remove non-numeric characters
      let cleanedValue = event.target.value.replace(/[^0-9]/g, '')
      // Remove leading zeros unless it's just "0"
      cleanedValue = cleanedValue.replace(/^0+/, '') || '0'
      // Update the field with the cleaned value
      item[field] = cleanedValue
      // 兜底逻辑：如果是 maxAmount，且小于等于 minAmount，则清空 maxAmount
      if (field === 'maxAmount') {
        const min = Number(item.minAmount)
        const max = Number(item.maxAmount)
        if (!isNaN(min) && !isNaN(max) && max <= min) {
          item.maxAmount = ''
        }
      }
    },
    // 新增：最大值失焦或回车后校验
    handleMaxAmountChange(item) {
      const min = Number(item.minAmount)
      const max = Number(item.maxAmount)
      if (!isNaN(min) && !isNaN(max) && max <= min) {
        item.maxAmount = ''
      }
    },
    // Prevent decimal point input for integer fields
    handleIntegerKeyPress(event) {
      // Prevent decimal point and non-numeric input
      if (event.key === '.' || event.key === ',' || !/[0-9]/.test(event.key)) {
        event.preventDefault()
        return false
      }
    },
    // Handle paste events for integer fields
    handleIntegerPaste(event, item, field) {
      // Prevent default paste behavior
      event.preventDefault()
      // Get clipboard content
      const pastedText = (event.clipboardData || window.clipboardData).getData('text')
      // Only keep numeric characters
      const cleanedText = pastedText.replace(/[^0-9]/g, '')
      // Remove leading zeros
      const finalText = cleanedText.replace(/^0+/, '') || '0'
      // Update the field with cleaned value
      item[field] = finalText
    },
    handleDistributionValueInput(eventOrValue, item) {
      let value = ''
      if (eventOrValue && typeof eventOrValue === 'object' && 'target' in eventOrValue) {
        value = String(eventOrValue.target.value ?? '')
      } else {
        value = String(eventOrValue ?? '')
      }

      if (item.distributionType === 'RATIO') {
        // 比例类型：允许正小数，最多两位小数
        let cleanedValue = value.replace(/[^0-9.]/g, '')
        // 只保留第一个小数点
        const parts = cleanedValue.split('.')
        if (parts.length > 2) {
          cleanedValue = parts[0] + '.' + parts.slice(1).join('')
        }
        // 限制小数点后最多两位
        if (cleanedValue.includes('.')) {
          const [intPart, decPart] = cleanedValue.split('.')
          cleanedValue = intPart + '.' + (decPart ? decPart.slice(0, 2) : '')
        }
        // 去除前导零（但保留 0.）
        if (cleanedValue.startsWith('0') && !cleanedValue.startsWith('0.')) {
          cleanedValue = cleanedValue.replace(/^0+/, '') || '0'
        }
        // 不允许 0 或 0.00
        if (cleanedValue === '0' || cleanedValue === '0.' || /^0\.0*$/.test(cleanedValue)) {
          cleanedValue = ''
        }
        item.distributionValue = cleanedValue
        if (value !== cleanedValue && eventOrValue && eventOrValue.target) {
          this.$nextTick(() => {
            eventOrValue.target.value = cleanedValue
          })
        }
      } else if (item.distributionType === 'FIXED') {
        // 固定类型：只允许正整数
        let cleanedValue = value.replace(/[^0-9]/g, '')
        // 去除前导零
        cleanedValue = cleanedValue.replace(/^0+/, '')
        // 不允许 0
        if (cleanedValue === '0') {
          cleanedValue = ''
        }
        item.distributionValue = cleanedValue
        if (value !== cleanedValue && eventOrValue && eventOrValue.target) {
          this.$nextTick(() => {
            eventOrValue.target.value = cleanedValue
          })
        }
      } else {
        // 未选择发放类型时，允许所有数字输入
        item.distributionValue = value.replace(/[^0-9.]/g, '')
      }
    },
    handleFixedKeyPress(event) {
      // 固定类型允许小数点，比例类型不允许
      // 只允许数字和（固定类型时）一个小数点
      // 需要通过事件 target 拿到当前 item 的 distributionType
      // 兼容性处理
      // 只允许数字和小数点
      if (event.target && event.target.type === 'text') {
        // 允许小数点
        if (!/[0-9.]/.test(event.key)) {
          event.preventDefault()
          return false
        }
        // 只允许一个小数点
        if (event.key === '.' && event.target.value.includes('.')) {
          event.preventDefault()
          return false
        }
      } else {
        // 比例类型，只允许数字
        if (event.key === '.' || event.key === ',' || !/[0-9]/.test(event.key)) {
          event.preventDefault()
          return false
        }
      }
    },
    handleFixedPaste(event) {
      // 阻止默认粘贴行为
      event.preventDefault()
      const pastedText = (event.clipboardData || window.clipboardData).getData('text')
      let cleanedText = ''
      // 判断类型
      const item = this.findItemByInput(event.target)
      if (item && item.distributionType === 'RATIO') {
        // 比例类型：只允许整数
        cleanedText = pastedText.replace(/[^0-9]/g, '')
        cleanedText = cleanedText.replace(/^0+/, '') || '0'
      } else {
        // 固定类型：允许整数和小数，最多两位小数
        cleanedText = pastedText.replace(/[^0-9.]/g, '')
        const parts = cleanedText.split('.')
        if (parts.length > 2) {
          cleanedText = parts[0] + '.' + parts.slice(1).join('')
        }
        if (cleanedText.includes('.')) {
          const [intPart, decPart] = cleanedText.split('.')
          cleanedText = intPart + '.' + (decPart ? decPart.slice(0, 2) : '')
        }
        if (cleanedText.startsWith('0') && !cleanedText.startsWith('0.')) {
          cleanedText = cleanedText.replace(/^0+/, '') || '0'
        }
      }
      // 手动插入清理后的文本
      const target = event.target
      const start = target.selectionStart
      const end = target.selectionEnd
      const currentValue = target.value
      const newValue = currentValue.substring(0, start) + cleanedText + currentValue.substring(end)
      target.value = newValue
      if (item) {
        item.distributionValue = newValue
      }
      target.setSelectionRange(start + cleanedText.length, start + cleanedText.length)
    },
    findItemByInput(input) {
      // 通过输入框找到对应的item对象
      for (const config of this.formData.ruleConfig) {
        for (const item of config.items) {
          if (item.distributionValue === input.value) {
            return item
          }
        }
      }
      return null
    },
    handleCycleValueBlur() {
      if (this.formData.cycleValue === 0) {
        this.formData.cycleValue = ''
      }
    }
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :id="pageCode" :key="pageCode" :schema="schema" @handleRoute="handleRoute" />

    <a-modal
      v-model="dialogVisible"
      :title="viewStatus === 'fix' ? '修改积分规则' : '新增积分规则'"
      :width="1100"
      :confirm-loading="loading"
      @ok="handleSubmit"
      @cancel="dialogVisible = false"
    >
      <a-spin :spinning="loading">
        <div class="form-container">
          <div class="form-item">
            <div class="form-label"><span class="required-star">*</span>规则名称：</div>
            <div class="form-content">
              <a-input v-model="formData.ruleName" :class="{ error: invalidFields.ruleName }" placeholder="请输入规则名称" :max-length="64" />
            </div>
          </div>

          <div class="form-item">
            <div class="form-label"><span class="required-star">*</span>业务编码：</div>
            <div class="form-content">
              <a-select
                v-model="formData.ruleScene"
                :class="{ error: invalidFields.ruleScene }"
                placeholder="请选择业务编码"
                style="width: 100%"
                :disabled="viewStatus === 'fix'"
              >
                <a-select-option
                  v-for="option in ruleSceneOptions"
                  :key="option.code"
                  :value="option.code"
                >
                  {{ option.name }}
                </a-select-option>
              </a-select>
            </div>
          </div>

          <div v-for="(config, configIndex) in formData.ruleConfig || []" :key="configIndex" class="currency-rule-container">
            <div class="currency-header">
              <div class="currency-left">
                <span class="currency-label">币种：</span>
                <a-select
                  v-model="config.currency"
                  :class="{ error: invalidFields.ruleConfig[configIndex] && invalidFields.ruleConfig[configIndex].currency }"
                  style="width: 120px; margin-left: 8px;"
                  placeholder="请选择币种"
                  :disabled="viewStatus === 'fix'"
                >
                  <a-select-option
                    v-for="option in currencyOptions"
                    :key="option.value"
                    :value="option.value"
                    :disabled="isCurrencySelected(option.value, configIndex)"
                  >
                    {{ option.label }}
                  </a-select-option>
                </a-select>
              </div>
              <div class="currency-right">
                <a-popconfirm
                  title="是否确认删除该币种？"
                  ok-text="确认"
                  cancel-text="取消"
                  :disabled="viewStatus === 'fix'"
                  @confirm="removeCurrencyRule(configIndex)"
                >
                  <a-button v-if="formData.ruleConfig.length > 1" type="link" class="delete-btn" :disabled="viewStatus === 'fix'">删除</a-button>
                </a-popconfirm>
              </div>
            </div>

            <div class="rule-table">
              <div class="rule-header">
                <div class="col-name"><span class="required-star">*</span>名称</div>
                <div class="col-min-value"><span class="required-star">*</span>最小值</div>
                <div class="col-symbol" />
                <div class="col-condition"><span class="required-star">*</span>触发事件</div>
                <div class="col-compare-symbol" />
                <div class="col-compare-value"><span class="required-star">*</span>最大值</div>
                <div class="col-reward-type"><span class="required-star">*</span>发放类型</div>
                <div class="col-reward-value"><span class="required-star">*</span>发放数量</div>
                <div class="col-action">操作</div>
              </div>

              <div v-for="(item, itemIndex) in config.items || []" :key="itemIndex" class="rule-row">
                <div class="col-name">
                  <a-input
                    v-model="item.name"
                    :class="{ error: invalidFields.ruleConfig[configIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex].name }"
                    placeholder="名称"
                    :disabled="viewStatus === 'fix'"
                  />
                </div>
                <div class="col-min-value">
                  <a-input-number
                    v-model="item.minAmount"
                    :class="{ error: invalidFields.ruleConfig[configIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex].minAmount }"
                    placeholder="最小值"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :disabled="viewStatus === 'fix'"
                    @input="handleIntegerInput($event, item, 'minAmount')"
                    @keypress="handleIntegerKeyPress"
                    @paste="handleIntegerPaste($event, item, 'minAmount')"
                  />
                </div>
                <div class="col-symbol">
                  <span>&lt;</span>
                </div>
                <div class="col-condition">
                  <a-select
                    v-model="item.event"
                    :class="{ error: invalidFields.ruleConfig[configIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex].event }"
                    style="width: 170px"
                    placeholder="请选择触发事件"
                    :disabled="viewStatus === 'fix'"
                  >
                    <a-select-option v-for="option in eventOptions" :key="option.code" :value="option.code">
                      {{ option.name }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="col-compare-symbol">
                  <span>&lt;=</span>
                </div>
                <div class="col-compare-value">
                  <a-input-number
                    v-model="item.maxAmount"
                    :class="{ error: invalidFields.ruleConfig[configIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex].maxAmount }"
                    placeholder="最大值"
                    :min="0"
                    :precision="0"
                    :step="1"
                    :disabled="viewStatus === 'fix'"
                    @input="handleIntegerInput($event, item, 'maxAmount')"
                    @keypress="handleIntegerKeyPress"
                    @paste="handleIntegerPaste($event, item, 'maxAmount')"
                    @change="handleMaxAmountChange(item)"
                  />
                </div>
                <div class="col-reward-type">
                  <a-select
                    v-model="item.distributionType"
                    :class="{ error: invalidFields.ruleConfig[configIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex].distributionType }"
                    style="width: 150px"
                    placeholder="请选择发放类型"
                    :disabled="viewStatus === 'fix'"
                  >
                    <a-select-option v-for="option in rewardTypeOptions" :key="option.code" :value="option.code">
                      {{ option.name }}
                    </a-select-option>
                  </a-select>
                </div>
                <div class="col-reward-value">
                  <a-input-number
                    v-model="item.distributionValue"
                    style="width: 125px;"
                    :class="{ error: invalidFields.ruleConfig[configIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex] && invalidFields.ruleConfig[configIndex].items[itemIndex].distributionValue }"
                    placeholder="发放数量"
                    :type="'number'"
                    :precision="item.distributionType === 'RATIO' ? 8 : 0"
                    :step="item.distributionType === 'RATIO' ? 0.00000001 : 1"
                    :min="0"
                    :disabled="viewStatus === 'fix'"
                    @input="handleDistributionValueInput($event, item)"
                    @keypress="handleFixedKeyPress($event)"
                    @paste="handleFixedPaste($event)"
                  />
                </div>
                <div class="col-action">
                  <a-popconfirm
                    title="是否确认删除该规则？"
                    ok-text="确认"
                    cancel-text="取消"
                    :disabled="viewStatus === 'fix'"
                    @confirm="removeRule(config, itemIndex)"
                  >
                    <a-button type="link" :disabled="viewStatus === 'fix' || config.items.length <= 1">删除</a-button>
                  </a-popconfirm>
                </div>
              </div>
            </div>

            <div class="add-rule">
              <a-button
                type="dashed"
                block
                :disabled="config.items.length >= 5 || viewStatus === 'fix'"
                @click="addRule(config)"
              >
                <a-icon type="plus" />添加
              </a-button>
            </div>
          </div>

          <div class="add-currency">
            <a-button
              type="dashed"
              block
              :disabled="formData.ruleConfig.length >= 5 || viewStatus === 'fix'"
              @click="addCurrencyRule"
            >
              <a-icon type="plus" />
            </a-button>
          </div>

          <div class="form-item">
            <div class="form-label"><span class="required-star">*</span>有效期：</div>
            <div class="form-content">
              <div class="flex-row">
                <a-input-number
                  v-model="formData.cycleValue"
                  :class="{ error: invalidFields.cycleValue }"
                  placeholder="请输入有效期"
                  style="min-width: 170px; margin-right: 8px;"
                  type="number"
                  :min="1"
                  @blur="handleCycleValueBlur"
                  :disabled="viewStatus === 'fix' || formData.cycleUnit === 'PERMANENT'"
                />
                <a-select
                  v-model="formData.cycleUnit"
                  :class="{ error: invalidFields.cycleUnit }"
                  style="width: 80px;"
                  placeholder="请选择单位"
                  :disabled="viewStatus === 'fix'"
                  @change="handleCycleUnitChange"
                >
                  <a-select-option v-for="option in expireUnitOptions" :key="option.code" :value="option.code">
                    {{ option.name }}
                  </a-select-option>
                </a-select>
              </div>
            </div>
          </div>

          <div class="form-item">
            <div class="form-label"><span class="required-star">*</span>生效时间：</div>
            <div class="form-content">
              <a-range-picker
                :value="dateRange"
                :class="{ error: invalidFields.dateRange }"
                :placeholder="['开始时间', '结束时间']"
                style="width: 100%"
                :show-time="{ defaultValue: defaultTimeRange }"
                format="YYYY-MM-DD HH:mm:ss"
                @change="handleDateRangeChange"
              />
            </div>
          </div>
        </div>
      </a-spin>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
.form-container {
  padding: 0 16px;
}

.form-item {
  display: flex;
  margin-bottom: 24px;
  align-items: center;
}

.form-label {
  width: 80px;
  text-align: right;
  margin-right: 8px;
  white-space: nowrap;
}

.form-content {
  flex: 1;
}

.currency-rule-container {
  border: 1px solid #e8e8e8;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;

  .currency-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    align-items: center;

    .currency-left {
      display: flex;
      align-items: center;
    }

    .currency-label {
      white-space: nowrap;
    }

    .currency-right {
      text-align: right;
    }
  }

  .rule-table {
    width: 100%;

    .rule-header, .rule-row {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
    }

    .rule-header {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }

    .col-name {
      width: 175px;
      padding-right: 8px;
    }

    .col-min-value {
      width: 105px;
      padding-right: 8px;
      padding-left: 8px;
    }

    .col-symbol {
      width: 20px;
      text-align: center;
    }

    .col-condition {
      width: 188px;
      padding: 0 8px;
    }

    .col-compare-symbol {
      width: 20px;
      text-align: center;
    }

    .col-compare-value {
      width: 105px;
      padding: 0 8px;
    }

    .col-reward-type {
      width: 170px;
      padding: 0 8px;
    }

    .col-reward-value {
      width: 125px;
      padding-right: 8px;
    }

    .col-action {
      width: 50px;
      text-align: center;
    }
  }
}

.add-currency {
  margin-bottom: 24px;
}

.add-rule {
  margin-top: 16px;
}

.required-star {
  color: #ff4d4f;
  margin-right: 2px;
}
.error {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255,77,79,0.1) !important;
}

.flex-row {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
