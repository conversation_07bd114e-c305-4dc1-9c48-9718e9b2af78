<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'fd_withdrawal_order',
      schema: {} 
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    handleRoute(bizId, userId, viewStatus) {
      this.$router.push('/checklistManage/increaseList/detail?bizId=' + bizId + '&userId=' + userId + '&viewStatus=' + viewStatus)
    }
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema" @handleRoute="handleRoute"/>
  </div>
</template>
