<template>
  <el-form ref="form" :model="user" :rules="rules" label-width="120px">
    <el-form-item label="旧密码" prop="oldPassword">
      <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" show-password/>
    </el-form-item>
    <el-form-item label="新密码" prop="newPassword">
      <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" show-password/>
    </el-form-item>
    <el-form-item label="确认密码" prop="confirmPassword">
      <el-input v-model="user.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
    </el-form-item>
    <el-form-item label="邮箱验证码" prop="emailCode">
      <el-input v-model="user.emailCode" placeholder="邮箱验证码" style="width:66%"/>
      <div class="login-code" style="width: 32%">
        <el-button v-if="!countDownIng" :loading="emailLoading" @click="getEmailCode('MODIFY_PASSWORD')" class="login-code-img">获取验证码</el-button>
        <el-button v-else class="login-code-img">{{countdown}}</el-button>
      </div>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateUserPwdByEmail } from "@/api/system/user";
import { getUserName } from '@/utils/user'
import { sendEmailCode } from "@/api/system/user";
export default {
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined,
        emailCode: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 12, max: 20, message: "长度在 12 到 20 个字符", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ],
        emailCode: [
          { required: true, message: "邮箱验证码不能为空", trigger: "blur" },
          { min: 6, max: 6, message: "长度为6个字符", trigger: "blur" }
        ]
      },
      emailLoading: false,
      timer: null,
      countdown: 120,
      countDownIng: false
    };
  },
  unmounted() {
    clearInterval(this.timer);
    this.countDownIng = false
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwdByEmail(this.user.oldPassword, this.user.newPassword, this.user.emailCode).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.formReset("form");
            this.$tab.closePage();
          });
        }
      });
    },
    close() {
      this.$tab.closePage();
    },
    formReset(ref) {
      this.$refs[ref].resetFields();
    },
    getEmailCode(verifyCodeType) {
      let isOk = true
      // 重设密码时已经输入了用户名
      const username = getUserName()
      if(!username) return
      
      this.emailLoading = true;
      sendEmailCode({
        username, 
        verifyCodeType
      }).then(res => {
        const {code, msg} = res;
        // 倒计时逻辑
        // 验证码已发送
        if(code === 200){
          this.$modal.msgSuccess("验证码已发送");
          // 页面倒计时逻辑
          this.countDownIng = true;
          // 计时器
          this.timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
              clearInterval(this.timer);
              this.countdown = 120;
              this.countDownIng = false
            }
          }, 1000);
          
        }else{
          this.$modal.msgError(msg||"验证码发送失败，请重试");
        }
        this.emailLoading = false;
      }).finally(() => {
        this.emailLoading = false;
      });
    },
  }
};
</script>
<style scoped>
.login-code {
  width: 33%;
  height: 38px;
  float: right;
}
.login-code-img {
  height: 38px;
  width: 100%;
}
</style>