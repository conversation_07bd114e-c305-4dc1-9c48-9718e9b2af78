<template>
  <div class="initiate-adjustment">
    <h4 class="initiate-adjustment__title">调单资料选择</h4>
    <a-card
      v-for="(question, index) in questions"
      :key="question.id"
      class="initiate-adjustment__question-card"
      :title="getQuestionTitle(index)"
    >
      <template #extra>
        <a-button
          v-if="canDeleteQuestion"
          type="text"
          danger
          @click="handleRemoveQuestion(index)"
        >
          删除
        </a-button>
      </template>
      <a-form layout="vertical" class="initiate-adjustment__form">
        <a-form-item
          label="关键词"
          :validate-status="getKeywordValidateStatus(question.keyword)"
          :help="getKeywordHelpText(question.keyword)"
          :required="true"
        >
          <a-textarea
            v-model="question.keyword"
            :max-length="500"
            :auto-size="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入关键词，最多500字"
            show-count
          />
        </a-form-item>
        <a-form-item
          label="问题描述"
          :required="true"
        >
          <editor v-model="question.description" :min-height="120" />
        </a-form-item>
      </a-form>
    </a-card>
    <div class="initiate-adjustment__add-btn">
      <a-button
        type="dashed"
        block
        :disabled="!canAddQuestion"
        @click="addQuestion"
      >
        添加问题
      </a-button>
    </div>
    <div class="initiate-adjustment__deadline">
      <h4 class="initiate-adjustment__deadline-title">调单截止时间设置</h4>
      <a-date-picker
        v-model="deadline"
        class="initiate-adjustment__date-picker"
        :disabled-date="disabledDate"
        placeholder="请选择截止日期"
        :required="true"
      />
    </div>
    <div class="initiate-adjustment__footer">
      <a-button
        type="primary"
        block
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        确定
      </a-button>
    </div>
  </div>
</template>

<script>
import { Modal } from 'ant-design-vue'
import { saveAdjustment, submitAdjustment } from '@/api/adjustmentList'
import moment from 'moment'
import { getAdjustmentDetail } from '@/api/adjustmentList'

// 问题数量限制常量
const MAX_QUESTIONS = 20 // 最大问题数量
const MIN_QUESTIONS = 1 // 最小问题数量
const MAX_KEYWORD_LENGTH = 500 // 关键词最大长度

// 固定值常量
const BIZ_CODE = 'RETRIEVAL_ORDER'
const QUESTION_TYPE = 'investigation-combine'

/**
 * 创建新的问题对象
 * @returns {Object} 包含id、keyword和description的问题对象
 */
function createQuestion() {
  return {
    id: String(Date.now()) + Math.floor(Math.random() * 1000),
    keyword: '',
    description: ''
  }
}

/**
 * 格式化问题数据为接口所需格式
 * @param {Array} questions - 问题列表
 * @returns {Array} 格式化后的问题数据
 */
function formatQuestions(questions) {
  return questions.map((question, index) => ({
    key: question.id,
    label: question.keyword,
    desc: question.description,
    value: {
      text: '', // 文本问题答案，由后端返回
      upload: [] // 上传文件，由后端返回
    },
    type: QUESTION_TYPE,
    visible: true,
    parentInfo: '',
    order: index + 1
  }))
}

export default {
  name: 'InitiateAdjustment',
  data() {
    return {
      questions: [createQuestion()], // 问题列表，初始包含一个问题
      deadline: null, // 截止时间
      formData: null, // 存储从路由传递过来的表单数据
      isSubmitting: false, // 添加提交状态标记
      bizScene: '', // 添加业务场景
      userId: '' // 添加用户ID
    }
  },
  computed: {
    /**
     * 判断是否可以添加新问题
     * @returns {boolean} 当问题数量小于最大值时返回true
     */
    canAddQuestion() {
      return this.questions.length < MAX_QUESTIONS
    },
    /**
     * 判断是否可以删除问题
     * @returns {boolean} 当问题数量大于最小值时返回true
     */
    canDeleteQuestion() {
      return this.questions.length > MIN_QUESTIONS
    }
  },
  created() {
    // 从路由参数中获取数据
    const { formData, bizScene, userId } = this.$route.query
    this.bizScene = bizScene
    this.userId = userId

    let parsedFormData = null
    if (formData) {
      try {
        // 只有是有效 JSON 字符串才解析
        parsedFormData = typeof formData === 'string' ? JSON.parse(formData) : formData
        this.formData = parsedFormData
        this.initFormData()
      } catch (error) {
        if (bizScene && userId) {
          this.fetchAdjustmentDetail(bizScene, userId)
        }
      }
    } else if (bizScene && userId) {
      // 没有formData时自动调接口
      this.fetchAdjustmentDetail(bizScene, userId)
    }
  },
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      if (!this.formData) return

      // 如果有jsonData，则使用jsonData中的数据
      if (this.formData.jsonData) {
        try {
          // jsonData已经是字符串，需要解析
          const jsonData = typeof this.formData.jsonData === 'string'
            ? JSON.parse(this.formData.jsonData)
            : this.formData.jsonData

          // 根据实际数据结构设置表单值
          if (Array.isArray(jsonData)) {
            this.questions = jsonData.map(q => ({
              id: q.key || String(Date.now() + Math.random()),
              keyword: q.label || '',
              description: q.desc || ''
            }))
          }

          // 设置截止时间
          if (this.formData.deadLineTime) {
            this.deadline = moment(this.formData.deadLineTime, 'YYYY-MM-DD HH:mm:ss')
            console.log('deadline:', this.deadline, moment.isMoment(this.deadline))
          }
        } catch (error) {
          console.error('解析jsonData失败:', error)
        }
      }
    },
    /**
     * 生成问题标题
     * @param {number} index - 问题索引
     * @returns {string} 格式化的问题标题
     */
    getQuestionTitle(index) {
      return `问题${index + 1}:`
    },
    /**
     * 添加新问题
     * 当问题数量未达到最大值时，添加一个新的问题对象
     */
    addQuestion() {
      if (this.canAddQuestion) {
        this.questions.push(createQuestion())
      }
    },
    /**
     * 删除问题
     * @param {number} index - 要删除的问题索引
     */
    handleRemoveQuestion(index) {
      Modal.confirm({
        title: '确认删除',
        content: '确定要删除这个问题吗？',
        okText: '确定',
        cancelText: '取消',
        maskClosable: false,
        keyboard: true,
        centered: true,
        onOk: () => {
          this.removeQuestion(index)
        }
      })
    },
    /**
     * 删除指定索引的问题
     * @param {number} index - 要删除的问题索引
     */
    removeQuestion(index) {
      if (this.canDeleteQuestion) {
        // 使用不可变的方式更新数组
        this.questions = this.questions.filter((_, i) => i !== index)
      }
    },
    /**
     * 禁用过去的日期
     * @param {Date} current - 当前日期
     * @returns {boolean} 如果是过去的日期返回true
     */
    disabledDate(current) {
      return current && current < new Date().setHours(0, 0, 0, 0)
    },
    /**
     * 获取关键词输入框的验证状态
     * @param {string} keyword - 关键词内容
     * @returns {string} 验证状态
     */
    getKeywordValidateStatus(keyword) {
      if (keyword.length > MAX_KEYWORD_LENGTH) return 'error'
      return ''
    },
    /**
     * 获取关键词输入框的帮助文本
     * @param {string} keyword - 关键词内容
     * @returns {string} 帮助文本
     */
    getKeywordHelpText(keyword) {
      if (keyword.length > MAX_KEYWORD_LENGTH) return '关键词不能超过500字'
      return ''
    },
    /**
     * 验证所有问题是否填写完整
     * @returns {boolean} 验证结果
     */
    validateQuestions() {
      return this.questions.every(question => {
        const isKeywordValid = question.keyword && question.keyword.trim().length > 0
        const isDescriptionValid = question.description && question.description.trim().length > 0
        return isKeywordValid && isDescriptionValid
      })
    },
    /**
     * 验证截止时间是否已选择
     * @returns {boolean} 验证结果
     */
    validateDeadline() {
      return this.deadline !== null
    },
    /**
     * 处理表单提交
     */
    handleSubmit() {
      // 验证所有问题
      if (!this.validateQuestions()) {
        this.$message.error('请完整填写所有问题的关键词和问题描述')
        return
      }

      // 验证截止时间
      if (!this.validateDeadline()) {
        this.$message.error('请选择调单截止时间')
        return
      }

      // 构建提交数据
      const submitData = {
        bizCode: BIZ_CODE,
        bizScene: this.bizScene,
        jsonData: JSON.stringify(formatQuestions(this.questions)),
        deadLineTime: this.deadline ? this.deadline.format('YYYY-MM-DD') : null,
        userId: this.userId,
        isAdmin: true
      }

      // 先保存数据
      saveAdjustment(submitData).then(saveResult => {
        console.log(saveResult.data, 'saveResult.data')
        if (!saveResult.data.data) {
          this.$message.error('保存数据失败，请重试')
          return
        }

        // 显示确认弹窗
        Modal.confirm({
          title: '发起调单',
          content: '是否确认发起调单？',
          okText: '确定',
          cancelText: '取消',
          maskClosable: false,
          keyboard: true,
          centered: true,
          onOk: () => {
            this.isSubmitting = true
            submitAdjustment({
              ...submitData,
              id: saveResult.data.data && saveResult.data.data.bizId
            }).then(result => {
              console.log(result, 'result提交提交提交')
              if (result.data.data) {
                this.$message.success('调单已成功发起')
                this.$tab.closePage()
              } else {
                this.$message.error((result.data && result.data.msg) || '提交失败，请重试')
              }
              this.isSubmitting = false
            }).catch(error => {
              console.error('提交失败:', error)
              this.isSubmitting = false
            })
          }
        })
      }).catch(error => {
        console.error('保存失败:', error)
      })
    },
    async fetchAdjustmentDetail(bizScene, userId) {
      try {
        const res = await getAdjustmentDetail({
          bizCode: 'RETRIEVAL_ORDER',
          userId,
          bizScene
        })
        const { status, jsonData } = res.data.data || {}
        if (status === 'INIT' && jsonData) {
          this.formData = res.data.data
          this.initFormData()
        }
        // 你可以根据需要处理其他状态
      } catch (err) {
        console.error('获取调单详情失败:', err)
      }
    }
  }
}
</script>

<style scoped>
/* 组件容器样式 */
.initiate-adjustment {
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

/* 标题样式 */
.initiate-adjustment__title {
  margin-bottom: 24px;
  font-weight: 600;
  font-size: 16px;
}

/* 问题卡片样式 */
.initiate-adjustment__question-card {
  margin-bottom: 16px;
  max-width: 600px;
  width: 100%;
}

/* 表单样式 */
.initiate-adjustment__form {
  width: 100%;
}

/* 覆盖表单项默认间距 */
:deep(.initiate-adjustment__form .ant-form-item) {
  margin-bottom: 12px;
}

/* 添加按钮容器样式 */
.initiate-adjustment__add-btn {
  margin-bottom: 32px;
  max-width: 600px;
  width: 100%;
}

/* 截止时间设置区域样式 */
.initiate-adjustment__deadline {
  margin-bottom: 24px;
  max-width: 600px;
  width: 100%;
}

/* 截止时间标题样式 */
.initiate-adjustment__deadline-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 500;
}

/* 日期选择器样式 */
.initiate-adjustment__date-picker {
  width: 100%;
}

/* 底部按钮容器样式 */
.initiate-adjustment__footer {
  max-width: 600px;
  width: 100%;
}
</style>
