<template>
  <div class="review-adjust">
    <!-- 用户提交资料区 -->
    <h4 class="review-adjust__section-title">用户提交资料</h4>
    <div v-for="item in jsonData" :key="item.key" class="review-adjust__block">
      <!-- 核心词 -->
      <div class="review-adjust__row">
        <span class="review-adjust__label">关键词：</span>
        <span class="review-adjust__value">{{ item.label }}</span>
      </div>
      <!-- 问题描述（富文本） -->
      <div class="review-adjust__row">
        <span class="review-adjust__label">问题描述：</span>
        <Editor v-model="item.desc" :read-only="true" class="review-adjust__desc" />
      </div>
      <!-- 答案（富文本） -->
      <div v-if="item.value && item.value.text" class="review-adjust__row">
        <span class="review-adjust__label">回复：</span>
        <span class="review-adjust__reply" v-html="item.value.text" />
      </div>
      <!-- 上传资料：图片和文件分开展示 -->
      <div v-if="item.value && item.value.upload && item.value.upload.length" class="review-adjust__row">
        <span class="review-adjust__label">上传资料：</span>
        <span>
          <template v-for="(file, idx) in item.value.upload">
            <!-- 图片：点击图片直接下载 -->
            <a
              v-if="/\.(jpe?g|png)$/i.test(file.path)"
              :key="'img-' + idx"
              :href="file.path"
              :download="getFileName(file.path)"
              style="display:inline-block;margin-right:8px;"
            >
              <img
                :src="file.path"
                alt="图片"
                class="review-img"
                loading="lazy"
                style="cursor:pointer;"
              />
            </a>
            <!-- PDF文件有分隔符 -->
            <span v-else-if="/\.pdf$/i.test(file.path)" :key="'pdf-' + idx" style="display:inline-block;margin-right:8px;">
              <a
                :href="file.path"
                :download="getFileName(file.path)"
                target="_blank"
                class="mr-2"
              >{{ getFileName(file.path) }}</a>
              <span v-if="idx !== item.value.upload.length - 1" style="color:#ccc;">、</span>
            </span>
            <!-- 其他类型不展示 -->
          </template>
        </span>
      </div>
    </div>

    <!-- 风控审核区 -->
    <h4 class="review-adjust__section-title review-adjust__section-title--audit">风控审核</h4>
    <div class="review-adjust__row">用户提交资料次数：{{ submitCount }}</div>
    <a-form
      :model="form"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 16 }"
      class="review-adjust__form"
    >
      <a-form-item label="审核结果" :required="true">
        <a-select
          v-model="form.result"
          :disabled="isDetail && form.result !== 'WAIT_REVIEW'"
          placeholder="请选择审核结果"
          @change="onResultChange"
        >
          <a-select-option v-for="item in resultOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item v-if="form.result === 'DECLINE'" label="驳回原因" :required="true">
        <a-textarea
          v-model="form.rejectReason"
          :disabled="isDetail"
          placeholder="请输入驳回原因"
          :rows="4"
          :maxlength="500"
          show-count
        />
      </a-form-item>
      <a-form-item
        v-if="form.result === 'PASS' || form.result === 'REJECTED'"
        label="调单质量"
        :required="true"
      >
        <a-select
          v-model="form.quality"
          :disabled="isDetail"
          placeholder="请选择调单质量"
        >
          <a-select-option value="LOW">低</a-select-option>
          <a-select-option value="MIDDLE">中</a-select-option>
          <a-select-option value="HIGH">高</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        v-if="form.result === 'PASS' || form.result === 'REJECTED'"
        label="调单审核意见"
        :required="true"
      >
        <Editor
          v-model="form.comment"
          :read-only="isDetail"
          :min-height="120"
          :max-length="500"
          placeholder="请输入审核意见"
        />
      </a-form-item>
      <div v-if="!isDetail" class="review-adjust__footer">
        <a-button type="primary" :loading="loading" block @click="onSubmit">确定</a-button>
      </div>
    </a-form>
  </div>
</template>

<script>
import { Modal } from 'ant-design-vue'
import Editor from '@/components/Editor/index.vue'
import { review } from '@/api/adjustmentList'

export default {
  name: 'ReviewAdjust',
  components: { Editor },
  data() {
    return {
      isDetail: false,
      jsonData: [],
      submitCount: 0,
      form: {
        result: '',
        rejectReason: '',
        quality: '',
        comment: ''
      },
      loading: false,
      bizCode: 'RETRIEVAL_ORDER',
      bizId: '',
      userId: ''
    }
  },
  computed: {
    resultOptions() {
      return [
        { value: 'PASS', label: '通过' },
        { value: 'DECLINE', label: '驳回' },
        { value: 'REJECTED', label: '拒绝' }
      ]
    }
  },
  created() {
    const query = this.$route.query
    this.isDetail = query.isDetail === 'true' || query.isDetail === true
    if (query.jsonData) {
      try {
        this.jsonData = typeof query.jsonData === 'string'
          ? JSON.parse(query.jsonData)
          : query.jsonData
      } catch (e) {
        this.jsonData = []
      }
    }
    if (query.submitCount) {
      this.submitCount = Number(query.submitCount)
    }
    // 可根据需要初始化form的其他字段
    const emptyStatus = ['INIT', 'UN_SUBMIT', 'WAIT_REVIEW', 'INVALID', 'CLOSED']
    this.form.result = emptyStatus.includes(query.detailStatus) ? '' : query.detailStatus
    if (query.rejectReason) this.form.rejectReason = query.rejectReason
    if (query.quality) this.form.quality = query.quality
    if (query.reviewAdvice) this.form.comment = query.reviewAdvice
    // 保存路由参数
    this.bizId = query.bizId || ''
    this.userId = query.userId || ''
  },
  methods: {
    stripHtml(html) {
      if (!html) return ''
      return html.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, '').trim()
    },
    onResultChange() {
      if (this.form.result !== 'DECLINE') this.form.rejectReason = ''
      if (this.form.result !== 'PASS' && this.form.result !== 'REJECTED') this.form.quality = ''
    },
    async onSubmit() {
      if (!this.form.result) {
        this.$message.error('请选择审核结果')
        return
      }
      if (this.form.result === 'DECLINE' && !this.form.rejectReason) {
        this.$message.error('请填写驳回原因')
        return
      }
      if ((this.form.result === 'PASS' || this.form.result === 'REJECTED') && !this.form.quality) {
        this.$message.error('请选择调单质量')
        return
      }
      if ((this.form.result === 'PASS' || this.form.result === 'REJECTED') && !this.stripHtml(this.form.comment)) {
        this.$message.error('请填写调单审核意见')
        return
      }
      Modal.confirm({
        title: '审核确认',
        content: `是否确认此次审核结果？\n审核结果：${this.resultOptions.find(opt => opt.value === this.form.result)?.label || ''}`,
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          this.loading = true
          const params = {
            bizCode: this.bizCode,
            bizId: this.bizId,
            userId: this.userId,
            reviewStatus: this.form.result === 'REJECTED' ? 'REJECT' : this.form.result,
            rejectReason: this.form.rejectReason
          }

          // 如果是通过或拒绝，需要添加质量和审核意见
          if (this.form.result === 'PASS' || this.form.result === 'REJECTED') {
            params.quality = this.form.quality
            params.reviewAdvice = this.form.comment
          }
          console.log(params, 'params')
          review(params)
            .then(() => {
              this.$message.success('审核成功')
              // 审核通过/拒绝后关闭当前页签
              this.$tab.closePage()
            })
            .catch(() => {
              this.$message.error('审核失败')
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    },
    getFileName(path) {
      if (!path) return ''
      return path.split('/').pop()
    }
  }
}
</script>

<style scoped>
.review-adjust {
  padding: 24px 32px;
  background: #fff;
}
.review-adjust__block {
  margin-bottom: 32px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 24px;
}
.review-adjust__row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}
.review-adjust__label {
  font-weight: bold;
  margin-right: 8px;
  font-size: 16px;
  width: 100px;
  flex-shrink: 0;
}
.review-adjust__desc {
  word-break: break-all;
  white-space: pre-wrap;
  flex: 1;
}
.review-adjust__form {
  margin-top: 24px;
  max-width: 600px;
}
.review-adjust__footer {
  margin-top: 24px;
}
.review-adjust__section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}
.review-adjust__section-title--audit {
  margin-top: 32px;
}
.review-img {
  display: inline-block;
  width: 160px;
  height: 160px;
  margin-right: 8px;
  margin-bottom: 8px;
  border: 1px solid #eee;
  border-radius: 4px;
  object-fit: contain;
}
.review-adjust__value {
  word-break: break-all;
  white-space: pre-wrap;
  flex: 1;
}
.review-adjust__reply {
  word-break: break-all;
  white-space: pre-wrap;
  flex: 1;
}
</style>
