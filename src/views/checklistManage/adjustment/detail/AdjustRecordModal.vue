<template>
  <a-modal
    :visible="visible"
    title="调单记录"
    :footer="null"
    width="700px"
    destroy-on-close
    @cancel="handleCancel"
  >
    <a-table
      :data-source="data"
      :columns="columns"
      :row-key="record => record.detailId"
      :pagination="pagination"
      :loading="loading"
      bordered
      size="middle"
      @change="handleTableChange"
    >
      <span slot="action" slot-scope="text, record">
        <a @click="handleView(record)">查看</a>
      </span>
    </a-table>
  </a-modal>
</template>

<script>
export default {
  name: 'AdjustRecordModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      columns: [
        { title: '提交资料时间', dataIndex: 'applyTime', key: 'applyTime' },
        { title: '审核时间', dataIndex: 'auditTime', key: 'auditTime' },
        { title: '操作人', dataIndex: 'reviewer', key: 'reviewer' },
        {
          title: '操作',
          key: 'action',
          scopedSlots: { customRender: 'action' }
        }
      ],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条记录`,
        pageSizeOptions: ['10', '20', '50', '100']
      }
    }
  },
  watch: {
    total: {
      handler(val) {
        this.pagination.total = val
      },
      immediate: true
    }
  },
  methods: {
    handleCancel() {
      this.$emit('update:visible', false)
    },
    handleView(record) {
      this.$emit('view', record)
    },
    handleTableChange(pagination) {
      this.pagination.current = pagination.current
      this.pagination.pageSize = pagination.pageSize
      this.$emit('change', {
        current: pagination.current,
        pageSize: pagination.pageSize
      })
    }
  }
}
</script>

<style scoped>
/* 可根据需要自定义样式 */
</style>
