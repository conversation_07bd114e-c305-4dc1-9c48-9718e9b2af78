<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import AdjustRecordModal from './detail/AdjustRecordModal.vue'
import { getAdjustmentDetailList, getAdjustmentDetailInfo, getAdjustmentDetailPage } from '@/api/adjustmentList'
export default defineComponent({
  components: { AdjustRecordModal },
  data() {
    return {
      pageCode: 'kun_user_adjustment_list',
      schema: {},
      recordModalVisible: false,
      recordList: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      currentBizId: null
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    handleRoute(userId, bizId) {
      getAdjustmentDetailPage({
        bizCode: 'RETRIEVAL_ORDER',
        bizId,
        userId
      })
        .then(res => {
          if (res.data && res.data.data) {
            this.$router.push({
              path: '/checklistManage/adjustment/detail/index',
              query: {
                isDetail: false,
                userId,
                ...res.data.data
              }
            })
          } else {
            this.$message.error('获取详情失败')
          }
        })
        .catch(() => {
          this.$message.error('获取详情失败')
        })
    },
    handleAdjust(bizId) {
      this.currentBizId = bizId
      this.pagination.current = 1
      this.fetchRecordList()
    },
    fetchRecordList() {
      this.loading = true
      getAdjustmentDetailList({
        bizId: this.currentBizId,
        bizCode: 'RETRIEVAL_ORDER',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize
      }).then(res => {
        if (res.data && res.data.data) {
          this.recordList = Array.isArray(res.data.data.data) ? res.data.data.data : []
          this.pagination.total = res.data.data.total || 0
        }
        this.recordModalVisible = true
      }).catch(() => {
        this.$message.error('获取调单记录失败')
      }).finally(() => {
        this.loading = false
      })
    },
    handlePageChange({ current, pageSize }) {
      this.pagination.current = current
      this.pagination.pageSize = pageSize
      this.fetchRecordList()
    },
    handleViewRecord(record) {
      getAdjustmentDetailInfo({ detailId: record.detailId, bizCode: 'RETRIEVAL_ORDER' }).then(res => {
        if (res.data && res.data.data) {
          this.$router.push({
            path: '/checklistManage/adjustment/detail/index',
            query: {
              isDetail: true,
              ...res.data.data
            }
          })
        } else {
          this.$message.error('获取详情失败')
        }
      }).catch(() => {
        this.$message.error('获取详情失败')
      })
    }
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema" @handleRoute="handleRoute" @handleAdjust="handleAdjust" />
    <AdjustRecordModal
      :visible.sync="recordModalVisible"
      :data="recordList"
      :total="pagination.total"
      :loading="loading"
      @change="handlePageChange"
      @view="handleViewRecord"
    />
  </div>
</template>
