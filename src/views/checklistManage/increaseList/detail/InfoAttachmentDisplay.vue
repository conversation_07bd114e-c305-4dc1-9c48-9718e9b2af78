<template>
  <div class="detail-container">
    <!-- 基础信息模块 -->
    <a-card class="mb16" title="基础信息">
      <a-descriptions :column="column" bordered>
        <a-descriptions-item
          v-for="([key, field]) in baseFields"
          :key="key"
          :label="field.label"
        >
          <a-tooltip :title="formData[key]">
            <span class="ellipsis">{{ formData[key] }}</span>
          </a-tooltip>
        </a-descriptions-item>
      </a-descriptions>
    </a-card>

    <!-- 附件信息模块 -->
    <a-card class="mb16" title="提供资料">
      <div class="row">
        <div v-for="([key, field]) in supplementFields" :key="key" class="col col-1-2">
          <div class="file-label">{{ field.label }}</div>
          <div class="file-list">
            <template v-if="field.type === 'input-text'">
              <a :href="field.value" target="_blank" class="file-link">{{ field.value }}</a>
            </template>
            <template v-else-if="field.value && field.value.length">
              <a v-for="(file, fidx) in field.value" :key="fidx" :href="file.path" target="_blank" class="file-link">
                <img v-if="isImage(file.path)" :src="file.path" :width="300" :height="150" class="file-img">
                <a-button v-else type="link" icon="paper-clip" :href="file.path" download>下载</a-button>
              </a>
            </template>
            <span v-else class="file-empty">暂无文件</span>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script>
const imageExtensions = [
  '.jpg', '.jpeg', '.png', '.webp', '.gif', '.bmp', '.svg', '.ico', '.tiff', '.jfif', '.pjpeg', '.pjp', '.avif'
]

function isImage(url) {
  if (!url) return false
  var lower = url.toLowerCase()
  return imageExtensions.some(function(ext) {
    return lower.endsWith(ext)
  })
}

export default {
  name: 'InfoAttachmentDisplay',
  props: {
    rawData: {
      type: Object,
      required: true
    },
    formData: {
      type: Object,
      required: true
    },
    column: {
      type: Number,
      default: 3
    }
  },
  computed: {
    baseFields() {
      return Object.entries(this.rawData)
        .filter(([_, v]) => v && v.visible !== false && v.parentInfo === 'basic')
    },
    supplementFields() {
      return Object.entries(this.rawData)
        .filter(([_, v]) => v && v.visible !== false && v.parentInfo === 'supplement')
    }
  },
  methods: {
    isImage
  }
}
</script>

<style scoped>
.detail-container {
  padding: 24px 24px 0px 24px;
}
.mb16 {
  margin-bottom: 16px;
}
.ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
}
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8px;
  margin-right: -8px;
}
.col {
  padding-left: 8px;
  padding-right: 8px;
  margin-bottom: 16px;
}
.col-1-3 {
  width: 100%;
}
@media (min-width: 900px) {
  .col-1-3 {
    width: 33.3333%;
  }
  .col-1-2 {
    width: 50%;
  }
}
.file-label {
  font-weight: 500;
  margin-bottom: 8px;
}
.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.file-link {
  display: block;
  color: #409eff;
}
.file-img {
  border-radius: 4px;
  border: 1px solid #eee;
}
.file-empty {
  color: #aaa;
}
</style>
