<script>
import InfoAttachmentDisplay from './InfoAttachmentDisplay.vue'
import { detail, userDetail, review } from '@/api/userManage/increaseList'

export default {
  name: 'IncreaseListDetail',
  components: { InfoAttachmentDisplay },
  data() {
    return {
      loading: false,
      status: 'pending',
      rawData: {},
      formData: {},
      auditor: '',
      auditRemark: '',
      auditResult: '',
      approveLimit: '',
      rejectReason: '',
      accountLimitInfos: [],
      accountLimitColumns: [
        { title: '币种', dataIndex: 'currencyCode', key: 'currencyCode' },
        { title: '日交易限额', dataIndex: 'hour24Transfer', key: 'hour24Transfer' },
        { title: '月交易限额', dataIndex: 'day30Transfer', key: 'day30Transfer' },
        { title: '日提现限额', dataIndex: 'hour24Withdraw', key: 'hour24Withdraw' },
        { title: '月提现限额', dataIndex: 'day30Withdraw', key: 'day30Withdraw' }
      ]
    }
  },
  computed: {
    bizId() {
      return this.$route.query.bizId
    },
    viewStatus() {
      return this.$route.query.viewStatus || 'edit'
    },
    userId() {
      return this.$route.query.userId
    },
    approvalLimitLabel() {
      const transactionType = this.rawData.transactionType?.value || ''
      if (transactionType === 'LEGAL') {
        return '审批额度（单位：万USD）'
      } else if (transactionType === 'DIGITAL') {
        return '审批额度（单位：万USDT）'
      }
      return '审批额度'
    },
    submitParams() {
      const params = {
        bizCode: 'LIMIT_RAISE',
        reviewStatus: this.auditResult,
        bizId: this.bizId,
        userId: this.userId,
        requestId: `${Date.now()}${Math.random().toString(36).substring(2, 15)}`.slice(0, 32)
      }
      if (this.auditResult === 'PASS') {
        const approveLimitValue = this.approveLimit ? (Number(this.approveLimit) * 10000).toString() : ''
        params.approveLimit = approveLimitValue
        params.extdJson1st = {
          approveLimit: approveLimitValue
        }
      }
      if (this.auditResult === 'REJECT') {
        params.rejectReason = this.rejectReason
      }
      return params
    }
  },
  created() {
    this.fetchDetailData()
    this.fetchUserDetail()
  },
  methods: {
    fetchDetailData() {
      this.loading = true
      detail({
        bizCode: 'LIMIT_RAISE',
        bizId: this.bizId,
        userId: this.userId
      }).then((res) => {
        if (res.data.data) {
          try {
            // 处理 jsonData 可能是字符串的情况
            const jsonData = typeof res.data.data.jsonData === 'string'
              ? JSON.parse(res.data.data.jsonData)
              : res.data.data.jsonData
            if (!jsonData || typeof jsonData !== 'object') {
              throw new Error('Invalid jsonData format')
            }
            // 解析后的数据
            this.rawData = jsonData
            this.initFormData()
            // 设置审核状态和相关信息
            this.status = res.data.data.status
            if (res.data.data.status === 'PASS') {
              this.auditResult = 'PASS'
              // 从 approveLimit 中获取审批额度
              if (res.data.data.extdJson1st.approveLimit) {
                this.approveLimit = (Number(res.data.data.extdJson1st.approveLimit) / 10000).toString()
              }
            } else if (res.data.data.status === 'REJECTED') {
              this.auditResult = 'REJECT'
              // 设置拒绝原因
              this.rejectReason = res.data.data.rejectReason || ''
            }
          } catch (error) {
            console.error('数据格式解析失败:', error)
          }
        }
      }).catch((error) => {
        console.error('API Error:', error)
        this.$message.error('获取详情数据失败')
      }).finally(() => {
        this.loading = false
      })
    },
    fetchUserDetail() {
      userDetail({ userId: this.userId }).then(res => {
        if (res.data && res.data.data.accountLimitInfos) {
          this.accountLimitInfos = res.data.data.accountLimitInfos.map((item, index) => ({
            ...item,
            id: `${item.currencyCode}-${index}`
          }))
        }
      }).catch(() => {
      })
    },
    initFormData() {
      this.formData = Object.fromEntries(
        Object.entries(this.rawData).map(([k, v]) => {
          if (v.type === 'upload-primary') {
            return [k, v.value || []] // 确保返回数组，即使是空数组
          }
          return [k, v.valueDesc] // 使用 valueDesc
        })
      )
    },
    validateForm() {
      if (!this.auditResult) {
        this.$message.error('请选择审核结果')
        return false
      }
      if (this.auditResult === 'PASS') {
        if (!this.approveLimit) {
          this.$message.error('请填写审批额度')
          return false
        }
        const value = Number(this.approveLimit)
        if (value <= 0 || !Number.isInteger(value)) {
          this.$message.error('审批额度必须为大于0的整数')
          return false
        }
      }
      if (this.auditResult === 'REJECT' && !this.rejectReason) {
        this.$message.error('请填写拒绝原因')
        return false
      }
      return true
    },
    handleSubmit() {
      if (!this.validateForm()) return
      if (this.loading) return // 防止重复提交
      this.loading = true
      review(this.submitParams)
        .then(() => {
          this.$message.success('已提交')
          this.$tab.closePage()
        })
        .catch(() => {
          this.$message.error('提交失败，请稍后重试')
        })
        .finally(() => {
          this.loading = false
        })
    },
    onUpdateForm(key, value) {
      this.formData[key] = value
    }
  }
}
</script>

<template>
  <div class="increase-detail-container">
    <InfoAttachmentDisplay
      :raw-data="rawData"
      :form-data="formData"
      :status="status"
      @submit="handleSubmit"
      @update-form="onUpdateForm"
    />
    <!-- 审核模块 -->
    <div class="audit-container">
      <a-card :title="'审核'" class="mb16">
        <a-table
          :columns="accountLimitColumns"
          :data-source="accountLimitInfos"
          row-key="id"
          size="small"
          class="mb16"
          :pagination="false"
        />
        <a-form layout="vertical" class="audit-form">
          <a-form-item label="审核结果" required>
            <a-select
              v-model="auditResult"
              placeholder="请选择审核结果"
              :disabled="viewStatus !== 'edit'"
            >
              <a-select-option value="PASS">通过</a-select-option>
              <a-select-option value="REJECT">拒绝</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item v-if="auditResult === 'PASS'" :label="approvalLimitLabel" required>
            <a-input
              v-model="approveLimit"
              placeholder="请输入审批额度"
              :disabled="viewStatus !== 'edit'"
              @input="(e) => approveLimit = e.target.value.replace(/[^\d]/g, '')"
            />
          </a-form-item>
          <a-form-item v-if="auditResult === 'REJECT'" label="拒绝原因" required>
            <a-textarea
              v-model="rejectReason"
              placeholder="请输入拒绝原因，最多256个字符"
              :disabled="viewStatus !== 'edit'"
              :max-length="256"
              :auto-size="{ minRows: 2, maxRows: 6 }"
            />
          </a-form-item>
        </a-form>
        <div v-if="viewStatus === 'edit'" class="audit-btns">
          <a-button type="primary" :loading="loading" @click="handleSubmit">确认</a-button>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped>
.increase-detail-container {
  padding: 24px 24px 24px 24px;
}
.audit-container {
  padding: 0 24px 24px 24px;
}
.mb16 {
  margin-bottom: 16px;
}
.ellipsis {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
}
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -8px;
  margin-right: -8px;
}
.col {
  padding-left: 8px;
  padding-right: 8px;
  margin-bottom: 16px;
}
.col-1-3 {
  width: 100%;
}
@media (min-width: 900px) {
  .col-1-3 {
    width: 33.3333%;
  }
  .col-1-2 {
    width: 50%;
  }
}
.file-label {
  font-weight: 500;
  margin-bottom: 8px;
}
.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.file-link {
  display: block;
}
.file-img {
  border-radius: 4px;
  border: 1px solid #eee;
}
.file-empty {
  color: #aaa;
}
.audit-form {
  max-width: 400px;
}
.audit-btns {
  margin-top: 24px;
}
</style>
