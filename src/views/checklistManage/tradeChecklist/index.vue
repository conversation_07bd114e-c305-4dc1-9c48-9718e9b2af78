<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  name: 'TradeChecklist',
  data() {
    return {
      pageCode: 'trade_checklist_list',
      schema: {}
    }
  },
  methods: {
    handleRoute(checklistId) {
      console.log(checklistId)
      this.$router.push('/checklistManage/tradeChecklist/detail?checklistId='+checklistId)
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema" @handleRoute="handleRoute"/>
  </div>
</template>
