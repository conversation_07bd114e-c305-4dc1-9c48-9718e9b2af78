<template>
  <el-dialog
    title="新增任务"
    :visible.sync="visible"
    width="600px"
    custom-class="el-dialog-underline"
    @close="closeModal"
    top="5vh"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="taskForm" size="small">
      <el-form-item label="任务名称" required prop="businessCode">
        <task-select
          v-model="form.businessCode"
          :multiple="false"
          @taskChange="taskChange"
        ></task-select>
      </el-form-item>
      <el-form-item label="任务名称细分" required prop="trxType">
        <el-select
          style="width: 100%"
          v-model="form.trxType"
          placeholder="请选择任务细分"
          @change="trxTypeChange"
        >
          <el-option
            v-for="trxType in trxTypes"
            :key="trxType.value"
            :label="trxType.label"
            :value="trxType.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="form.trxType"
        label="数据格式"
        required
        prop="dataFormat"
      >
        <el-select
          style="width: 100%"
          v-model="form.dataFormat"
          disabled
          placeholder="请选择数据格式"
          clearable
        >
          <el-option
            v-for="task in dataFormats"
            :key="task.value"
            :label="task.label"
            :value="task.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="form.trxType"
        label="处理Handler"
        required
        prop="handler"
      >
        <el-select
          style="width: 100%"
          v-model="form.handler"
          disabled
          placeholder="请选择处理Handler"
          clearable
        >
          <el-option
            v-for="task in handlers"
            :key="task.value"
            :label="task.label"
            :value="task.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单完成日期" required prop="orderCompletionTime">
        <el-date-picker
          v-model="form.orderCompletionTime"
          style="width: 100%"
          placeholder="请选择时间"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { addFetchTask } from "@/api/reconciliation";
import TaskSelect from "@/components/TaskSelect";
export default {
  name: "AddTaskModal",
  components: { TaskSelect },
  data() {
    return {
      visible: false,
      loading: false,
      dataFormats: [],
      trxTypes: [],
      handlers: [],
      form: {
        businessCode: "",
        trxType: "",
        dataFormat: "",
        fetchConfigNo: "",
        handler: "",
        orderCompletionTime: "",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() >= new Date().setHours(0, 0, 0, 0); // 禁用今天及以后日期
        },
      },
      rules: {
        businessCode: [
          { required: true, message: "任务名称不能为空", trigger: "change" },
        ],
        trxType: [
          {
            required: true,
            message: "任务细分不能为空",
            trigger: "change",
          },
        ],
        dataFormat: [
          { required: true, message: "数据格式不能为空", trigger: "change" },
        ],
        handler: [
          { required: true, message: "处理Handler不能为空", trigger: "change" },
        ],
        orderCompletionTime: [
          {
            required: true,
            message: "订单完成日期不能为空",
            trigger: "change",
          },
          {
            required: true,
            message: "订单完成日期不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    taskChange(task) {
      if (task) {
        this.trxTypes = task.bizType;
      } else {
        this.trxTypes = [];
      }
      if (this.form.trxType) {
        this.form.trxType = "";
      }
    },
    trxTypeChange(code) {
      const curBizType = this.trxTypes.find((item) => item.value === code);
      if (curBizType) {
        this.handlers = curBizType.fetchHandle;
        this.dataFormats = curBizType.dataFormats;
        this.form.handler = this.handlers[0]?.value;
        this.form.dataFormat = this.dataFormats[0]?.value;
        this.form.fetchConfigNo = curBizType.fetchConfigNo;
      } else {
        this.handlers = [];
        this.dataFormats = [];
        this.form.handler = "";
        this.form.dataFormat = "";
        this.form.fetchConfigNo = "";
      }
    },
    async handleConfirm() {
      this.$refs["taskForm"].validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            const data = await addFetchTask({
              fetchConfigNo: this.form.fetchConfigNo,
              orderCompletionTime: this.form.orderCompletionTime,
            });
            this.visible = false;
            this.$emit("addSuccess");
            this.$modal.msgSuccess("新增成功");
          } catch (error) {
          } finally {
            this.loading = false;
          }
        }
      });
    },
    show() {
      this.visible = true;
    },
    closeModal() {
      // 关闭弹窗需要重置交易类型
      this.trxTypes = [];
      this.resetForm("taskForm");
    },
  },
};
</script>

<style></style>
