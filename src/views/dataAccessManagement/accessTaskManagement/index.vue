<template>
  <div class="access-task app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryForm"
      label-position="top"
      size="small"
      :inline="true"
    >
      <el-row type="flex" style="flex-wrap: wrap" :gutter="16">
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="订单完成日期:"
            style="width: 100%"
            prop="orderCompletionTime"
          >
            <limited-date-picker
              v-model="queryParams.orderCompletionTime"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="任务名称:"
            style="width: 100%"
            prop="businessCodes"
          >
            <task-select v-model="queryParams.businessCodes"></task-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="拉取完成时间:"
            style="width: 100%"
            prop="fetchTime"
          >
            <limited-date-picker
              v-model="queryParams.fetchTime"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item label="任务状态:" style="width: 100%" prop="taskStatus">
            <el-select
              style="width: 100%"
              v-model="queryParams.taskStatus"
              multiple
              placeholder="请选择任务状态"
              clearable
            >
              <el-option
                v-for="dict in taskStatus"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col style="width: auto; margin-left: auto; align-self: flex-end">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              :loading="loading"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              icon="el-icon-refresh"
              :loading="loading"
              size="mini"
              @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >新增</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        prop="fetchNo"
        label="拉取编号"
        min-width="200"
      ></el-table-column>
      <el-table-column prop="businessName" label="任务名称" min-width="200">
      </el-table-column>
      <el-table-column prop="bizTypeName" label="任务细分" width="200">
      </el-table-column>
      <el-table-column label="订单完成日期" prop="orderDate" width="150">
        <template slot-scope="scope">
          <span>{{ scope.row.orderDate }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="任务状态" width="100">
        <template slot-scope="scope">
          <dict-tag :options="taskStatus" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column prop="dataFormat" label="数据格式" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dataFormats" :value="scope.row.dataFormat" />
        </template>
      </el-table-column>
      <el-table-column prop="handler" label="处理Handler" width="200">
      </el-table-column>
      <el-table-column prop="finishTime" label="拉取完成时间" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.finishTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150">
      </el-table-column>
      <el-table-column prop="operatorId" label="操作人" width="100">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status === 'FAIL'"
            size="mini"
            type="text"
            @click="handleRetry(scope.row)"
            >重试</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <add-task-modal ref="addTaskModal" @addSuccess="addSuccess" />
    <remark-modal
      ref="remarkModal"
      @success="getList"
      :required="false"
      title="重试"
      :addRemark="handleRetryConfirm"
    />
  </div>
</template>

<script>
import { checkFetchTaskQuery, taskRetry } from "@/api/reconciliation";
import AddTaskModal from "./addTaskModal.vue";
import RemarkModal from "@/components/RemarkModal";
import { deepClone } from "@/utils";
import { findDictsByType } from "@/utils/storage";
import LimitedDatePicker from "@/components/LimitedDatePicker";
import TaskSelect from "@/components/TaskSelect";
import { getLastDays, addMinutesAndSeconds } from "@/utils/index";

export default {
  name: "AccessTask",
  components: { AddTaskModal, RemarkModal, LimitedDatePicker, TaskSelect },
  data() {
    return {
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        orderCompletionTime: getLastDays(),
        businessCodes: [],
        fetchTime: [],
        taskStatus: [],
      },
      taskStatus: findDictsByType("pull_task_status"),
      dataFormats: findDictsByType("reconciliation_pull_data_format"),
      taskNames: findDictsByType("pull_task_name"),
      showSearch: true,
      remarkForm: {
        remark: "",
      },
      loading: false,
      visible: false,
      retryLoading: false,
      total: 0,
      retryId: null,
      rules: {
        remark: [
          { required: true, message: "备注不能为空", trigger: "change" },
        ],
      },
      dataList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      try {
        this.loading = true;
        const queryForm = deepClone(this.queryParams);
        const [orderCompletionTimeStart, orderCompletionTimeEnd] =
          queryForm.orderCompletionTime || [];
        const [fetchStartTime, fetchEndTime] =
          addMinutesAndSeconds(queryForm.fetchTime) || [];
        queryForm.orderCompletionTimeStart = orderCompletionTimeStart || null;
        queryForm.orderCompletionTimeEnd = orderCompletionTimeEnd || null;
        queryForm.fetchStartTime = fetchStartTime || null;
        queryForm.fetchEndTime = fetchEndTime || null;
        delete queryForm.orderCompletionTime;
        delete queryForm.fetchTime;
        const res = await checkFetchTaskQuery(queryForm);
        const { data: list, total } = res.data.data;
        this.dataList = list;
        this.total = total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    // 重置筛选条件
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.pageNo = 1;
      this.handleQuery();
    },
    // 新增拉取任务
    handleAdd() {
      this.$refs.addTaskModal.show();
    },
    // 重试定时任务
    handleRetry(row) {
      this.$refs["remarkModal"].show();
      this.retryId = row.fetchNo;
    },
    // 新增操作
    addSuccess() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    // 确定重试
    handleRetryConfirm(remarkForm) {
      return new Promise(async (resolve, reject) => {
        try {
          const data = await taskRetry({
            fetchNo: this.retryId,
            remark: remarkForm.remark,
          });
          resolve(data);
          this.$modal.msgSuccess("重试成功");
        } catch (error) {
          console.log(error, "error");
          this.getList();
          reject(error);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.access-task {
  .el-form-item__content {
    width: 100%;
  }
}
</style>
