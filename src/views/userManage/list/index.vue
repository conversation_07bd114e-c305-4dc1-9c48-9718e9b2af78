<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
import { getAdjustmentDetail } from '@/api/adjustmentList'

// 状态常量
const ADJUSTMENT_STATUS = {
  INIT: 'INIT',
  UN_SUBMIT: 'UN_SUBMIT',
  WAIT_REVIEW: 'WAIT_REVIEW',
  PASS: 'PASS',
  REJECTED: 'REJECTED',
  INVALID: 'INVALID',
  CLOSED: 'CLOSED'
}

// 路由常量
const ROUTES = {
  NEW_PAGE: '/checklistManage/adjustment/detail/initiateAdjustment'
}

// 需要跳转到列表页的状态
const LIST_PAGE_STATUS = [ADJUSTMENT_STATUS.UN_SUBMIT, ADJUSTMENT_STATUS.WAIT_REVIEW]

export default defineComponent({
  data() {
    return {
      pageCode: 'kun_user_list',
      schema: {},
      bizScene: ''
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    handleRoute(userId) {
      this.$router.push('/userManage/list/detail?userId=' + userId)
    },
    routeTo(route) {
      this.$router.push(route)
    },
    /**
     * 处理路由跳转
     * @param {string} path - 目标路径
     * @param {string} bizScene - 调整类型
     * @param {string} userId - 用户ID
     * @param {Object} [extraData] - 额外需要传递的数据
     */
    handleRouteJump(path, bizScene, userId, extraData = null) {
      const query = { bizScene, userId }
      if (extraData) {
        Object.assign(query, extraData)
      }
      this.$router.push({
        path,
        query
      })
    },
    /**
     * 获取调整类型并处理相关逻辑
     * @param {string} bizScene - 调整类型
     * @param {string} userId - 用户ID
     */
    getAdjustmentType(bizScene, userId) {
      console.log(bizScene, 'bizScene')
      console.log(userId, 'userId')
      this.bizScene = bizScene

      getAdjustmentDetail({
        bizCode: 'RETRIEVAL_ORDER',
        userId,
        bizScene: this.bizScene
      }).then(res => {
        const { bizId, status, jsonData } = res.data.data
        // 如果bizId不存在，直接跳转新增页面
        if (!bizId) {
          this.handleRouteJump(ROUTES.NEW_PAGE, bizScene, userId)
          return
        }
        // 如果是INIT状态且有jsonData，跳转新增页面并传递数据
        if (status === ADJUSTMENT_STATUS.INIT && jsonData) {
          this.handleRouteJump(ROUTES.NEW_PAGE, bizScene, userId, { formData: res.data.data })
          return
        }
        // 存在进行中的调单，给出提示，不再跳转
        if (LIST_PAGE_STATUS.includes(status)) {
          this.$message.warning('存在进行中的调单，无法再次发起调单！')
          return
        }
        // 其他情况跳转新增页面
        this.handleRouteJump(ROUTES.NEW_PAGE, bizScene, userId)
      }).catch(err => {
        console.log(err, 'err')
      })
    }
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema" @handleRoute="handleRoute" @routeTo="routeTo" @getAdjustmentType="getAdjustmentType" />
  </div>
</template>
