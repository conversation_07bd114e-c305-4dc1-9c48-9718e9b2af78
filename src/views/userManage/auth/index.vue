<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  name:'Auth',
  data() {
    return {
      pageCode: 'kun_user_auth_list',
      schema: {} 
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    toAuditPage(authId, userId, email) {
      this.$router.push('/userManage/auditPage/audit?authId='+(authId||'0')+'&userId='+(userId||'0')+'&email='+(email||''))
    },
    toPersonalAuditPage(authId, userId, email) {
      this.$router.push('/userManage/personalAuditPage/audit?authId='+(authId||'0')+'&userId='+(userId||'0')+'&email='+(email||''))
    },
    toLookPage(authId, userId) {
      this.$router.push('/userManage/auditPage/look?authId='+(authId||'0')+'&userId='+(userId||'0'))
    },
    toPersonalLookPage(authId, userId) {
      this.$router.push('/userManage/personalAuditPage/look?authId='+(authId||'0')+'&userId='+(userId||'0'))
    }
  },
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema"  @toAuditPage='toAuditPage' @toLookPage='toLookPage' @toPersonalAuditPage='toPersonalAuditPage' @toPersonalLookPage='toPersonalLookPage'/>
  </div>
</template>
