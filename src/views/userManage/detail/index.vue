<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'kun_user_detail',
      schema: {} 
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  },
  methods: {
    toLookPage(authId, userId) {
      this.$router.push('/userManage/auditPage/look?authId='+(authId||'0')+'&userId='+(userId||'0'))
    },
    toPersonalLookPage(authId, userId) {
      this.$router.push('/userManage/personalAuditPage/look?authId='+(authId||'0')+'&userId='+(userId||'0'))
    }
  },
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema" @toLookPage='toLookPage' @toPersonalLookPage='toPersonalLookPage'/>
  </div>
</template>
