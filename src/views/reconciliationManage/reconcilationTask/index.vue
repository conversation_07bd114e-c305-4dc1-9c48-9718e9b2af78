<template>
  <div class="access-task app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryForm"
      label-position="top"
      size="small"
      :inline="true"
    >
      <el-row type="flex" style="flex-wrap: wrap" :gutter="16">
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="对账完成时间:"
            style="width: 100%"
            prop="checkCompletionTime"
          >
            <limited-date-picker
              v-model="queryParams.checkCompletionTime"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            style="width: 100%"
            label="任务名称:"
            prop="businessCode"
          >
            <task-select
              v-model="queryParams.businessCode"
              pageType="reconciliation"
            ></task-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="订单完成日期:"
            style="width: 100%"
            prop="orderCompletionTime"
          >
            <limited-date-picker
              v-model="queryParams.orderCompletionTime"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item label="任务状态:" style="width: 100%" prop="taskStatus">
            <el-select
              style="width: 100%"
              v-model="queryParams.taskStatus"
              multiple
              placeholder="请选择任务状态"
              clearable
            >
              <el-option
                v-for="dict in taskStatus"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="大交易类型:"
            style="width: 100%"
            prop="checkType"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.checkType"
              multiple
              placeholder="请选择大交易类型"
              clearable
            >
              <el-option
                v-for="dict in bigTrxTypes"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col style="width: auto; margin-left: auto; align-self: flex-end">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQuery"
              :loading="loading"
              >搜索</el-button
            >
            <el-button
              :loading="loading"
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >下载</el-button
            >
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              >手动对账</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        prop="checkNo"
        label="对账编号"
        min-width="200"
      ></el-table-column>
      <el-table-column prop="businessName" label="任务名称" min-width="200">
      </el-table-column>
      <el-table-column prop="bizSource" label="业务来源" width="150">
      </el-table-column>
      <el-table-column prop="checkTypeNm" label="大交易类型" width="200">
      </el-table-column>
      <el-table-column prop="isOuterCheckNm" label="对账类型" width="150">
      </el-table-column>
      <el-table-column prop="status" label="任务状态" width="200">
        <template slot-scope="scope">
          <dict-tag :options="taskStatus" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column prop="orderDate" label="订单完成日期" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.orderDate }}</span>
        </template>
      </el-table-column>
      <el-table-column label="对账完成时间" prop="completionTime" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.completionTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="200">
      </el-table-column>
      <el-table-column prop="operatorNo" label="操作人" width="150">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.status === 'ERROR_OCCURRED'"
            size="mini"
            type="text"
            @click="handleRetry(scope.row)"
            >重试</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <add-modal ref="addModal" @addSuccess="getList" />
    <el-dialog
      title="重试"
      :visible.sync="visible"
      width="600px"
      @close="closeModal"
      top="5vh"
      custom-class="el-dialog-underline"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-form :model="remarkForm" ref="remarkForm" size="small">
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="remarkForm.remark"
            placeholder="请输入备注"
            clearable
            maxlength="50"
            show-word-limit
            size="small"
            style="margin-bottom: 20px"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          :loading="retryLoading"
          @click="handleRetryConfirm"
          >确 定</el-button
        >
        <el-button @click="visible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AddModal from "./addModal.vue";
import { findDictsByType } from "@/utils/storage";
import { deepClone } from "@/utils";
import { checkTaskQuery, checkTaskRetry } from "@/api/reconciliation";
import LimitedDatePicker from "@/components/LimitedDatePicker";
import TaskSelect from "@/components/TaskSelect";
import { getLastDays, addMinutesAndSeconds } from "@/utils/index";

export default {
  name: "ReconcilationTask",
  components: { AddModal, LimitedDatePicker, TaskSelect },
  data() {
    return {
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        checkCompletionTime: [],
        businessCode: [],
        taskStatus: [],
        checkType: [],
        orderCompletionTime: getLastDays(),
      },
      showSearch: true,
      taskStatus: findDictsByType("reconciliation_pull_task_status"),
      reconciliationTypes: findDictsByType("reconciliation_types"),
      bigTrxTypes: findDictsByType("big_deal_types"),
      remarkForm: {
        remark: "",
      },
      loading: false,
      visible: false,
      retryLoading: false,
      total: 0,
      retryId: null,
      dataList: [],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    processingParams(params) {
      const queryForm = deepClone(params);
      const [startCompletionTime, endCompletionTime] = addMinutesAndSeconds(
        queryForm.checkCompletionTime || []
      );
      const [orderCompletionTimeStart, orderCompletionTimeEnd] =
        addMinutesAndSeconds(queryForm.orderCompletionTime || []);
      delete queryForm.checkCompletionTime;
      delete queryForm.orderCompletionTime;
      queryForm.startCompletionTime = startCompletionTime || null;
      queryForm.endCompletionTime = endCompletionTime || null;
      queryForm.startTime = orderCompletionTimeStart || null;
      queryForm.endTime = orderCompletionTimeEnd || null;

      return queryForm;
    },
    async getList() {
      try {
        this.loading = true;
        const queryForm = this.processingParams(this.queryParams);
        const res = await checkTaskQuery(queryForm);
        const { data: list, total } = res.data.data;
        this.dataList = list;
        this.total = total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    // 重置筛选条件
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.pageNo = 1;
      this.handleQuery();
    },
    // 新增拉取任务
    handleAdd() {
      this.$refs.addModal.show();
    },
    // 重试操作
    handleRetry(row) {
      this.visible = true;
      this.retryId = row.checkNo;
    },
    closeModal() {
      this.resetForm("remarkForm");
      this.retryId = null;
    },
    // 确定重试
    async handleRetryConfirm() {
      try {
        this.retryLoading = true;
        await checkTaskRetry({
          checkNo: this.retryId,
          remark: this.remarkForm.remark,
        });
        this.visible = false;
        this.closeModal();
        this.$modal.msgSuccess("重试成功");
        this.getList();
      } catch (error) {
        this.getList();
      } finally {
        this.retryLoading = false;
      }
    },
    // 导出对账任务
    handleExport() {
      const queryForm = this.processingParams(this.queryParams);
      delete queryForm.pageNo;
      delete queryForm.pageSize;
      this.download(
        "check/task/export",
        {},
        `对账任务明细_${new Date().getTime()}.xlsx`,
        {},
        queryForm
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.access-task {
  .el-form-item__content {
    width: 100%;
  }
}
</style>
