<template>
  <el-dialog
    title="手动对账"
    :visible.sync="visible"
    width="600px"
    @close="closeModal"
    top="5vh"
    custom-class="el-dialog-underline"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="reconciliationForm" size="small">
      <el-form-item label="任务名称" required prop="businessCode">
        <el-select
          style="width: 100%"
          v-model="form.businessCode"
          placeholder="请选择任务名称"
          clearable
          @change="taskChange"
        >
          <el-option
            v-for="task in dict.type.check_task_name"
            :key="task.value"
            :label="task.label"
            :value="task.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="大交易类型" required prop="checkType">
        <el-select
          style="width: 100%"
          v-model="form.checkType"
          multiple
          placeholder="请选择大交易类型"
          clearable
        >
          <el-option
            v-for="task in bigTrxTypes"
            :key="task.value"
            :label="task.label"
            :value="task.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="对账类型" required prop="sourceType">
        <el-select
          style="width: 100%"
          v-model="form.sourceType"
          placeholder="请选择对账类型"
          clearable
        >
          <el-option
            v-for="task in reconciliationTypes"
            :key="task.value"
            :label="task.label"
            :value="task.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单完成日期" required prop="orderDate">
        <el-date-picker
          v-model="form.orderDate"
          style="width: 100%"
          placeholder="请选择日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
        ></el-date-picker>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { checkTaskAdd } from "@/api/reconciliation";
import TaskSelect from "@/components/TaskSelect";
import { findDictsByType } from "@/utils/storage";

export default {
  name: "AddReconciliationFormModal",
  components: { TaskSelect },
  dicts: ["check_task_name"],
  data() {
    return {
      visible: false,
      loading: false,
      reconciliationTypes: findDictsByType("reconciliation_types"),
      originBigTrxTypes: findDictsByType("big_deal_types"),
      bigTrxTypes: [],
      task: null,
      form: {
        businessCode: "",
        orderDate: "",
        checkType: [],
        sourceType: "OUTER_SOURCE",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() >= new Date().setHours(0, 0, 0, 0); // 禁用今天及以后日期
        },
      },
      rules: {
        businessCode: [
          { required: true, message: "任务名称不能为空", trigger: "change" },
        ],
        sourceType: [
          { required: true, message: "对账类型不能为空", trigger: "change" },
        ],
        checkType: [
          {
            required: true,
            type: "array",
            message: "大交易类型不能为空",
            trigger: "change",
          },
        ],
        orderDate: [
          {
            required: true,
            message: "订单完成日期不能为空",
            trigger: "change",
          },
          {
            required: true,
            message: "订单完成日期不能为空",
            trigger: "blur",
          },
        ],
      },
    };
  },
  methods: {
    async handleConfirm() {
      this.$refs["reconciliationForm"].validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            const data = await checkTaskAdd({
              ...this.form,
              businessName: this.task.label,
            });
            this.visible = false;
            this.$emit("addSuccess");
            this.$modal.msgSuccess("新增成功");
          } catch (error) {
          } finally {
            this.loading = false;
          }
        }
      });
    },
    taskChange(code) {
      if (code) {
        const tasks = this.dict.type.check_task_name;
        const task = tasks.find((item) => item.value === code);
        this.task = task;
        const types = task.raw.remark.split(",");

        const checkTypes = this.originBigTrxTypes.filter((item) => {
          return types.includes(item.value);
        });

        if (JSON.stringify(checkTypes) !== JSON.stringify(this.bigTrxTypes)) {
          this.bigTrxTypes = checkTypes;
          this.form.checkType = [];
        }
      } else {
        this.bigTrxTypes = [];
        this.form.checkType = [];
      }
    },
    show() {
      this.visible = true;
    },
    closeModal() {
      this.bigTrxTypes = [];
      this.resetForm("reconciliationForm");
    },
  },
};
</script>

<style></style>
