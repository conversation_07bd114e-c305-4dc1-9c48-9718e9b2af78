<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryForm"
      label-position="top"
      size="small"
      :inline="true"
    >
      <el-row type="flex" style="flex-wrap: wrap" :gutter="16">
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="对账日期:" style="width: 100%" prop="checkDate">
            <limited-date-picker
              v-model="queryParams.checkDate"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item
            style="width: 100%"
            label="渠道名称:"
            prop="channelCode"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.channelCode"
              multiple
              placeholder="请选择渠道名称"
              clearable
            >
              <el-option
                v-for="dict in channelNames"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select> </el-form-item
        ></el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="币种:" style="width: 100%" prop="currency">
            <el-select
              style="width: 100%"
              v-model="queryParams.currency"
              multiple
              placeholder="请选择币种"
              clearable
            >
              <el-option
                v-for="dict in CURRENCYS"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select> </el-form-item
        ></el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="差异金额:" style="width: 100%" prop="amounts">
            <amount-range
              v-model="queryParams.amounts"
            ></amount-range> </el-form-item
        ></el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item
            label="任务名称:"
            style="width: 100%"
            prop="businessCode"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.businessCode"
              multiple
              placeholder="请选择任务名称"
              clearable
            >
              <el-option
                v-for="dict in taskNames"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select> </el-form-item
        ></el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }"
          ><el-form-item
            label="差异结果:"
            style="width: 100%"
            prop="diffResults"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.diffResults"
              multiple
              placeholder="请选择差异结果"
              clearable
            >
              <el-option
                v-for="dict in diffResults"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select> </el-form-item
        ></el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="订单日期:" style="width: 100%" prop="orderDate">
            <limited-date-picker
              v-model="queryParams.orderDate"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col style="width: auto; margin-left: auto; align-self: flex-end">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              :loading="loading"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              icon="el-icon-refresh"
              :loading="loading"
              size="mini"
              @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >下载</el-button
            >
          </el-form-item></el-col
        >
      </el-row>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column prop="id" label="序号" width="150"></el-table-column>
      <el-table-column prop="businessName" label="任务名称" min-width="200">
      </el-table-column>
      <el-table-column prop="bizSource" label="业务来源" width="150">
      </el-table-column>
      <el-table-column prop="channelCodeNm" label="渠道名称" width="200">
      </el-table-column>
      <el-table-column label="币种" width="150" prop="currency">
      </el-table-column>
      <el-table-column label="链" width="150" prop="chain"> </el-table-column>
      <el-table-column prop="checkDate" label="对账日期" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.checkDate }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderDate" label="订单日期" width="200">
      </el-table-column>
      <el-table-column prop="leftAmount" label="渠道总金额" width="150">
        <template slot-scope="scope">
          {{ addThousandSeparator(scope.row.leftAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="rightAmount" label="记账总金额" width="150">
        <template slot-scope="scope">
          {{ addThousandSeparator(scope.row.rightAmount) }}
        </template>
      </el-table-column>
      <el-table-column prop="diffAmount" label="差异金额" width="150">
        <template #header>
          <span
            >差异金额
            <el-tooltip
              placement="top"
              content="【差异金额】=【渠道总金额】-【记账总金额】"
            >
              <i class="el-icon-question"></i>
            </el-tooltip>
          </span>
        </template>
        <template slot-scope="scope">
          <span :style="{ color: scope.row.diffAmount > 0 ? '#1890ff' : '' }">
            {{ addThousandSeparator(scope.row.diffAmount) }}</span
          >
        </template>
      </el-table-column>
      <el-table-column prop="diffResult" label="差异结果" width="120">
        <template slot-scope="scope">
          <dict-tag
            :options="diffResults"
            :value="scope.row.diffResult"
          ></dict-tag>
        </template>
      </el-table-column>

      <el-table-column prop="remark" label="差异备注" width="200">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            v-if="
              !scope.row.remark && scope.row.diffResult === 'HAS_DIFFERENCE'
            "
            size="mini"
            type="text"
            @click="handleRemark(scope.row)"
            >备注</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <remark-modal
      ref="remarkModal"
      title="备注"
      :addRemark="handleRetryConfirm"
    />
  </div>
</template>

<script>
import AmountRange from "@/components/AmountRange";
import RemarkModal from "@/components/RemarkModal";
import { deepClone } from "@/utils";
import { findDictsByType } from "@/utils/storage";
import { checkBalanceQuery, checkBalanceDeal } from "@/api/reconciliation";
import LimitedDatePicker from "@/components/LimitedDatePicker";
import TaskSelect from "@/components/TaskSelect";
import { getLastDays, addMinutesAndSeconds } from "@/utils/index";

export default {
  name: "ChannelBalance",
  components: {
    AmountRange,
    RemarkModal,
    LimitedDatePicker,
    TaskSelect,
  },
  data() {
    return {
      DISCREPANCY_TYPE: findDictsByType("reconciliation_status"),
      diffResults: findDictsByType("diff_result"),
      CURRENCYS: findDictsByType("kun_currency"),
      channelNames: findDictsByType("reconciliation_channel_names"),
      taskNames: findDictsByType("balance_task_name"),
      showSearch: true,
      total: 0,
      balanceSnapshotNo: null,
      loading: false,
      dataList: [],
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        checkDate: [],
        orderDate: getLastDays(),
        channelCode: [],
        currency: [],
        amounts: [],
        businessCode: [],
        diffResults: [],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    processingParams(params) {
      const [checkDateStart, checkDateEnd] = addMinutesAndSeconds(
        params.checkDate
      );
      const [orderDateStart, orderDateEnd] = addMinutesAndSeconds(
        params.orderDate
      );
      const [diffAmountMin, diffAmountMax] = params.amounts;
      const queryForm = deepClone(params);
      queryForm.checkDateStart = checkDateStart || null;
      queryForm.checkDateEnd = checkDateEnd || null;
      queryForm.orderDateStart = orderDateStart || null;
      queryForm.orderDateEnd = orderDateEnd || null;
      queryForm.diffAmountMin = diffAmountMin;
      queryForm.diffAmountMax = diffAmountMax;
      delete queryForm.checkDate;
      delete queryForm.orderDate;
      delete queryForm.amounts;
      return queryForm;
    },
    async getList() {
      try {
        this.loading = true;
        const queryForm = this.processingParams(this.queryParams);
        const res = await checkBalanceQuery(queryForm);
        const { data: list, total } = res.data.data;
        this.dataList = list;
        this.total = total;
        this.loading = false;
      } catch (error) {
        console.log(error, "eer");
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.pageNo = 1;
      this.handleQuery();
    },
    handleExport() {
      const queryForm = this.processingParams(this.queryParams);
      delete queryForm.pageNo;
      delete queryForm.pageSize;
      this.download(
        "check/balance/export",
        {},
        `渠道余额核对明细_${new Date().getTime()}.xlsx`,
        {},
        queryForm
      );
    },
    handleRemark(row) {
      this.balanceSnapshotNo = row.balanceSnapshotNo;
      this.$refs.remarkModal.show();
    },
    handleRetryConfirm(remarkForm) {
      return new Promise(async (resolve, reject) => {
        try {
          const data = await checkBalanceDeal({
            balanceSnapshotNo: this.balanceSnapshotNo,
            remark: remarkForm.remark,
          });
          resolve(data);
          this.$modal.msgSuccess("处理成功");
          this.getList();
        } catch (error) {
          console.log(error, "error");
          reject(error);
        }
      });
    },
  },
};
</script>

<style></style>
