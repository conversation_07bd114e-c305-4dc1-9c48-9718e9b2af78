<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryForm"
      size="small"
      label-position="top"
      :inline="true"
    >
      <el-row type="flex" style="flex-wrap: wrap" :gutter="16">
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item
            label="对账完成时间:"
            style="width: 100%"
            prop="check_date"
          >
            <limited-date-picker
              v-model="queryParams.check_date"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item
            label="任务名称:"
            style="width: 100%"
            prop="businessCode"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.businessCode"
              multiple
              placeholder="请选择任务名称"
              clearable
            >
              <el-option
                v-for="dict in taskNames"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item style="width: 100%" label="交易类型:" prop="bizType">
            <el-select
              style="width: 100%"
              v-model="queryParams.bizType"
              multiple
              placeholder="请选择交易类型"
              clearable
            >
              <el-option
                v-for="tx in bizTypes"
                :key="tx.value"
                :label="tx.label"
                :value="tx.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item
            style="width: 100%"
            label="差错类型:"
            prop="discrepancyTypes"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.discrepancyTypes"
              multiple
              placeholder="请选择差错类型"
              clearable
            >
              <el-option
                v-for="tx in discrepancyTypes"
                :key="tx.value"
                :label="tx.label"
                :value="tx.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="币种:" style="width: 100%" prop="currency">
            <el-select
              style="width: 100%"
              v-model="queryParams.currency"
              multiple
              placeholder="请选择币种"
              clearable
            >
              <el-option
                v-for="dict in CURRENCYS"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="处理状态:" style="width: 100%" prop="status">
            <el-select
              style="width: 100%"
              v-model="queryParams.status"
              multiple
              placeholder="请选择处理状态"
              clearable
            >
              <el-option
                v-for="dict in PROCESSING_STATUS"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="对账编号:" style="width: 100%" prop="checkNo">
            <el-input
              v-model="queryParams.checkNo"
              style="width: 100%"
              placeholder="请输入对账主键"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="订单编号:" style="width: 100%" prop="bizNo">
            <el-input
              v-model="queryParams.bizNo"
              style="width: 100%"
              placeholder="请输入订单编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :sm="{ span: 12 }" :lg="{ span: 8 }">
          <el-form-item label="订单日期:" style="width: 100%" prop="orderDate">
            <limited-date-picker
              v-model="queryParams.orderDate"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col style="width: auto; margin-left: auto; align-self: flex-end">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              :loading="loading"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              :loading="loading"
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >下载</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5"> </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="dataList">
      <el-table-column
        prop="batchNo"
        label="对账批次"
        min-width="200"
      ></el-table-column>
      <el-table-column
        prop="checkCompleteTime"
        label="对账完成时间"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.checkCompleteTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="businessName" label="任务名称" width="200">
      </el-table-column>
      <el-table-column prop="bizSource" label="业务来源" width="150">
      </el-table-column>
      <el-table-column prop="bizTypeNm" label="交易类型" width="200">
      </el-table-column>
      <el-table-column prop="checkTypeNm" label="大交易类型" width="100">
      </el-table-column>
      <el-table-column width="150">
        <template slot="header">
          <div>交易（换入）币种</div>
          <div>换出币种</div>
        </template>
        <template slot-scope="scope">
          <div style="min-height: 20px">{{ scope.row.leftCurrency }}</div>
          <div style="min-height: 20px; margin-top: 5px">
            {{ scope.row.rightCurrency }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="checkNo" label="对账编号" width="200">
      </el-table-column>
      <el-table-column prop="leftAmount" label="银行侧金额" min-width="200">
        <template slot-scope="scope">
          <div style="min-height: 20px">
            {{ addThousandSeparator(scope.row.leftAmount) }}
          </div>
          <div style="min-height: 20px; margin-top: 5px">
            {{ addThousandSeparator(scope.row.leftAmountOut) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="rightAmount" label="平台订单金额" min-width="200">
        <template slot-scope="scope">
          <div style="min-height: 20px">
            {{ addThousandSeparator(scope.row.rightAmount) }}
          </div>
          <div style="min-height: 20px; margin-top: 5px">
            {{ addThousandSeparator(scope.row.rightAmountOut) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="银行手续费" prop="leftFeeAmount" width="200">
        <template slot-scope="scope">
          {{ addThousandSeparator(scope.row.leftFeeAmount) }}
        </template>
      </el-table-column>
      <el-table-column label="平台手续费" prop="rightFeeAmount" width="200">
        <template slot-scope="scope">
          {{ addThousandSeparator(scope.row.rightFeeAmount) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="orderCompleteTime"
        label="银行完成时间"
        width="200"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.orderCompleteTime }}</span>
        </template>
      </el-table-column>

      <el-table-column label="订单日期" prop="orderDate" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.orderDate }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="channelCodeNm" label="渠道名称" width="120">
      </el-table-column>
      <el-table-column prop="riskFlagNm" label="风险判断" width="120">
      </el-table-column>
      <el-table-column
        prop="bizNo"
        label="订单编号"
        width="200"
      ></el-table-column>
      <el-table-column prop="mistakeTypeNm" label="差错类型" width="150">
      </el-table-column>
      <el-table-column prop="status" label="处理状态" width="120">
        <template slot-scope="scope">
          <dict-tag
            :options="PROCESSING_STATUS"
            :value="scope.row.status"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column prop="processType" label="处理类型" width="120">
        <template slot-scope="scope">
          <dict-tag
            :options="resolutionMethods"
            :value="scope.row.processType"
          ></dict-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="200">
      </el-table-column>
      <el-table-column prop="operatorNo" label="处理人" width="100">
      </el-table-column>
      <el-table-column label="处理时间" prop="operatorTime" width="200">
        <template slot-scope="scope">
          <span>{{ scope.row.operatorTime }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        width="150"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            v-if="scope.row.status === 'UNPROCESSED'"
            type="text"
            @click="handleConfirm(scope.row)"
            >差错处理</el-button
          >
          <a
            v-if="scope.row.uploadFile"
            size="mini"
            target="_blank"
            style="color: #409eff; font-size: 12px; margin-left: 5px"
            :href="scope.row.uploadFile"
            type="text"
            >下载文件</a
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <confirm-discrepancy ref="confirm" @addSuccess="getList" />
  </div>
</template>

<script>
import ConfirmDiscrepancy from "./confirm.vue";
import { deepClone } from "@/utils";
import { findDictsByType } from "@/utils/storage";
import { checkMistakeQuery } from "@/api/reconciliation";
import LimitedDatePicker from "@/components/LimitedDatePicker";
import TaskSelect from "@/components/TaskSelect";
import { getLastDays, addMinutesAndSeconds } from "@/utils/index";

export default {
  name: "Discrepancy",
  components: {
    ConfirmDiscrepancy,
    LimitedDatePicker,
    TaskSelect,
  },
  data() {
    return {
      bizTypes: [],
      discrepancyTypes: findDictsByType("error_resolution_types"),
      resolutionMethods: findDictsByType("error_resolution_methods"),
      CURRENCYS: findDictsByType("reconcilation_currencys"),
      PROCESSING_STATUS: findDictsByType("error_handling_status"),
      DISCREPANCY_TYPE: findDictsByType("reconciliation_status"),
      RISKASSESSMENT: findDictsByType("risk_summary"),
      taskNames: [],
      balanceTaskNames: findDictsByType("balance_task_name"),
      loading: false,
      dataList: [],
      showSearch: true,
      hasInit: false, // 标记是否已经初始化
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        check_date: [],
        orderDate: getLastDays(),
        businessCode: [],
        bizType: [],
        currency: [],
        discrepancyTypes: [],
        status: ["UNPROCESSED"],
        checkNo: "",
        bizNo: "",
      },
      total: 0,
    };
  },
  created() {
    const allTasks = findDictsByType("check_task_name");

    this.taskNames = allTasks.filter(
      (item) => !this.balanceTaskNames.find((bt) => bt.value === item.value)
    );
    if (this.$route.query.refresh) {
      this.queryParams.status = [];
      this.queryParams.check_date = [];
      this.queryParams.orderDate = [];
    }
    this.hasInit = true;
    this.init();
  },
  activated() {
    if (!this.hasInit) {
      if (this.$route.query.refresh) {
        this.queryParams.status = [];
        this.queryParams.orderDate = [];
      }
      this.init();
    }
  },
  methods: {
    init() {
      const query = this.$route.query;
      if (query.checkNo) {
        this.queryParams.checkNo = query.checkNo;
      }
      this.$store
        .dispatch("task/getTaskList")
        .then((res) => {
          this.bizTypes = res.bizTypesList;
        })
        .catch((err) => {
          console.log(err, "err");
        });
      this.getList().then(() => {
        this.hasInit = false;
      });
    },
    processingParams(params) {
      const queryForm = deepClone(params);
      const [checkDateStart, checkDateEnd] = addMinutesAndSeconds(
        queryForm.check_date
      );
      const [orderDateStart, orderDateEnd] = addMinutesAndSeconds(
        queryForm.orderDate
      );
      delete queryForm.check_date;
      delete queryForm.orderDate;
      queryForm.checkDateStart = checkDateStart || null;
      queryForm.checkDateEnd = checkDateEnd || null;
      queryForm.orderDateStart = orderDateStart || null;
      queryForm.orderDateEnd = orderDateEnd || null;
      return queryForm;
    },
    async getList() {
      try {
        this.loading = true;
        const queryForm = this.processingParams(this.queryParams);
        const res = await checkMistakeQuery(queryForm);
        const { data: list, total } = res.data.data;
        this.dataList = list;
        this.total = total;
        this.loading = false;
      } catch (error) {
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.pageNo = 1;
      this.queryParams.checkNo = "";
      this.queryParams.status = ["UNPROCESSED"];
      this.queryParams.check_date = [];
      this.queryParams.orderDate = getLastDays();
      this.handleQuery();
    },
    handleExport() {
      const queryForm = this.processingParams(this.queryParams);
      delete queryForm.pageNo;
      delete queryForm.pageSize;
      this.download(
        "check/mistake/export",
        {},
        `差错明细_${new Date().getTime()}.xlsx`,
        {},
        queryForm
      );
    },
    jumpDetail(orderId) {
      this.$router.push(`/kunPayOrderManage/orderDetail?orderId=${orderId}`);
    },
    handleConfirm(row) {
      this.$refs["confirm"].show(row.mistakeNo);
    },
  },
};
</script>

<style></style>
