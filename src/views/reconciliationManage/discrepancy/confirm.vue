<template>
  <el-dialog
    title="差错处理"
    :visible.sync="visible"
    width="600px"
    @close="closeModal"
    custom-class="el-dialog-underline"
    top="5vh"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="taskForm" size="small">
      <el-form-item label="处理方式" required prop="processType">
        <el-select
          style="width: 100%"
          v-model="form.processType"
          placeholder="请选择处理方式"
          clearable
        >
          <el-option
            v-for="task in discrepancyTypes"
            :key="task.value"
            :label="task.label"
            :value="task.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          placeholder="请输入备注"
          clearable
          size="small"
          maxlength="50"
          show-word-limit
          style="margin-bottom: 20px"
        />
      </el-form-item>
      <el-form-item label="上传文件">
        <el-upload
          class="upload"
          drag
          :limit="1"
          :http-request="customUpload"
          :on-remove="handleRemove"
          :before-upload="beforeUpload"
          :file-list="fileList"
          action=""
          accept=".zip"
          :on-exceed="handleExceed"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            只能上传.zip文件，文件大小不能超过 30MB
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button
        type="primary"
        :disabled="uploading"
        :loading="loading"
        @click="handleConfirm"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { findDictsByType } from "@/utils/storage";
import { checkMistakeDeal, checkFileUpload } from "@/api/reconciliation";
export default {
  name: "ConfirmDiscrepancy",
  data() {
    return {
      visible: false,
      loading: false,
      uploading: false,
      discrepancyTypes: findDictsByType("error_resolution_methods"),
      fileList: [],
      form: {
        mistakeNo: "",
        processType: "",
        remark: "",
        uploadFile: "",
      },
      rules: {
        processType: [
          { required: true, message: "请选择处理方式", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    // 处理文件变更
    handleFileChange(file, fileList) {
      this.uploading = true;
      this.fileList = []; // 先清空 fileList，等上传完成后再更新
    },
    // 自定义上传
    async customUpload(param) {
      const file = param.file;
      const formData = new FormData();
      formData.append("file", file);
      try {
        this.uploading = true;
        const res = await checkFileUpload(formData);
        this.form.uploadFile = res.data?.data?.packageUrl;
        this.fileList = [param.file];
        this.$message.success("文件上传成功！");
      } catch (error) {
        this.fileList = [];
      } finally {
        this.uploading = false;
      }
    },
    async handleConfirm() {
      this.$refs["taskForm"].validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            const data = await checkMistakeDeal(this.form);
            this.visible = false;
            this.handleRemove();
            this.$emit("addSuccess");
            this.$modal.msgSuccess("处理成功");
          } catch (error) {
          } finally {
            this.loading = false;
          }
        }
      });
    },
    handleRemove() {
      this.form.uploadFile = "";
    },
    show(mistakeNo) {
      this.form.mistakeNo = mistakeNo;
      this.visible = true;
    },
    closeModal() {
      this.fileList = [];
      this.handleRemove();
      this.resetForm("taskForm");
    },
    handleExceed(files, fileList) {
      this.$message.warning("当前限制选择 1 个文件");
    },
    beforeUpload(file) {
      const maxSize = 30 * 1024 * 1024; // 30MB

      // 检查文件大小
      if (file.size > maxSize) {
        this.$message.error("文件大小不能超过 30MB！");
        return false; // 阻止文件上传
      }

      return true; // 允许上传
    },
  },
};
</script>

<style></style>
