<template>
  <div class="result-summary app-container">
    <el-form
      :model="queryParams"
      v-show="showSearch"
      ref="queryForm"
      size="small"
      label-position="top"
      :inline="true"
    >
      <el-row type="flex" style="flex-wrap: wrap" :gutter="16">
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            style="width: 100%"
            label="对账完成时间:"
            prop="orderDate"
          >
            <limited-date-picker
              v-model="queryParams.orderDate"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            style="width: 100%"
            label="任务名称:"
            prop="businessCodes"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.businessCodes"
              multiple
              placeholder="请选择任务名称"
              clearable
            >
              <el-option
                v-for="dict in taskNames"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="差异类型:"
            style="width: 100%"
            prop="mistakeTypes"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.mistakeTypes"
              multiple
              placeholder="请选择差异类型"
              clearable
            >
              <el-option
                v-for="dict in DISCREPANCY_TYPE"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="订单日期:"
            style="width: 100%"
            prop="checkCompletionTime"
          >
            <limited-date-picker
              v-model="queryParams.checkCompletionTime"
            ></limited-date-picker>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }"
          ><el-form-item
            style="width: 100%"
            label="资损/资盈:"
            prop="riskFlags"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.riskFlags"
              multiple
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="dict in RISKASSESSMENT"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            style="width: 100%"
            label="渠道名称:"
            prop="channelCode"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.channelCode"
              multiple
              placeholder="请选择渠道名称"
              clearable
            >
              <el-option
                v-for="dict in channelNames"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select> </el-form-item
        ></el-col>
        <el-col :xs="{ span: 24 }" :md="{ span: 12 }" :xl="{ span: 8 }">
          <el-form-item
            label="大交易类型:"
            style="width: 100%"
            prop="checkType"
          >
            <el-select
              style="width: 100%"
              v-model="queryParams.checkType"
              multiple
              placeholder="请选择大交易类型"
              clearable
            >
              <el-option
                v-for="dict in bigTrxTypes"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col style="width: auto; margin-left: auto; align-self: flex-end">
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              :loading="loading"
              @click="handleQuery"
              >搜索</el-button
            >
            <el-button
              :loading="loading"
              icon="el-icon-refresh"
              size="mini"
              @click="resetQuery"
              >重置</el-button
            >
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              >下载</el-button
            >
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList">
      <el-table-column prop="checkNo" label="对账编号" min-width="200">
      </el-table-column>
      <el-table-column prop="businessName" label="任务名称" min-width="200">
      </el-table-column>
      <el-table-column prop="bizSource" label="业务来源" width="150">
      </el-table-column>
      <el-table-column prop="channelName" label="渠道名称" width="200">
      </el-table-column>
      <el-table-column prop="checkType" label="大交易类型" width="200">
      </el-table-column>
      <el-table-column width="150">
        <template slot="header">
          <div>银行总笔数</div>
          <div>平台总笔数</div>
        </template>
        <template slot-scope="scope">
          <div>{{ scope.row.bankTotalCount }}</div>
          <div>{{ scope.row.kunTotalCount }}</div>
        </template>
      </el-table-column>
      <el-table-column width="150">
        <template slot="header">
          <div>银行总金额</div>
          <div>平台总金额</div>
        </template>
        <template slot-scope="scope">
          <div>{{ addThousandSeparator(scope.row.bankTotalAmount) }}</div>
          <div>{{ addThousandSeparator(scope.row.kunTotalAmount) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="mistakeTypeName" label="差异类型" width="200">
      </el-table-column>
      <el-table-column prop="mistakeType" width="150">
        <template slot="header">
          <div>银行单边笔数</div>
          <div>平台单边笔数</div>
        </template>
        <template slot-scope="scope">
          <div>{{ scope.row.bankUnilateralCount }}</div>
          <div>{{ scope.row.platformUnilateralCount }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="mistakeType" label="" width="150">
        <template slot="header">
          <div>银行单边金额</div>
        </template>
        <template slot-scope="scope">
          <div>{{ addThousandSeparator(scope.row.bankUnilateralAmount) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="mistakeType" label="" width="150">
        <template slot="header">
          <div>平台单边金额</div>
        </template>
        <template slot-scope="scope">
          <div>
            {{ addThousandSeparator(scope.row.platformUnilateralAmount) }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="amountMismatchCount"
        label="金额不平笔数"
        width="200"
      >
      </el-table-column>
      <el-table-column
        prop="amountMismatchSum"
        label="金额不平金额"
        width="200"
      >
        <template slot-scope="scope">
          <div>{{ addThousandSeparator(scope.row.amountMismatchSum) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="riskFlagName" label="风险概括" width="200">
      </el-table-column>
      <el-table-column prop="orderDate" label="订单日期" width="200">
      </el-table-column>
      <el-table-column
        label="对账完成时间"
        prop="checkCompletionTime"
        width="200"
      >
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            v-if="
              scope.row.mistakeTypeName &&
              !scope.row.mistakeTypeName.includes('无差异')
            "
            type="text"
            @click="handleJump(scope.row)"
            >查看明细</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNo"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { deepClone } from "@/utils";
import { findDictsByType } from "@/utils/storage";
import { checkSummaryQuery } from "@/api/reconciliation";
import LimitedDatePicker from "@/components/LimitedDatePicker";
import TaskSelect from "@/components/TaskSelect";
import { getLastDays, addMinutesAndSeconds } from "@/utils/index";

export default {
  name: "ResultsSummary",
  components: { LimitedDatePicker, TaskSelect },
  data() {
    return {
      DISCREPANCY_TYPE: findDictsByType("discrepancy_types"),
      RISKASSESSMENT: findDictsByType("risk_summary"),
      balanceTaskNames: findDictsByType("balance_task_name"),
      channelNames: findDictsByType("reconciliation_channel_names"),
      bigTrxTypes: findDictsByType("big_deal_types").filter(
        (item) => !item.value.includes("BALANCE")
      ),
      taskNames: [],
      showSearch: true,
      loading: false,
      total: 0,
      queryParams: {
        pageNo: 1,
        pageSize: 20,
        checkCompletionTime: getLastDays(),
        businessCodes: [],
        mistakeTypes: [],
        orderDate: [],
        riskFlags: [],
        checkType: [],
      },
      dataList: [],
    };
  },
  created() {
    const allTasks = findDictsByType("check_task_name");

    this.taskNames = allTasks.filter(
      (item) => !this.balanceTaskNames.find((bt) => bt.value === item.value)
    );
    this.getList();
  },
  methods: {
    processingParams(params) {
      const queryForm = deepClone(params);
      const checkCompletionTime = params.checkCompletionTime || [];
      const orderDate = addMinutesAndSeconds(params.orderDate);
      queryForm.checkDateStart = checkCompletionTime[0] || null;
      queryForm.checkDateEnd = checkCompletionTime[1] || null;
      queryForm.orderDateBegin = orderDate[0] || null;
      queryForm.orderDateEnd = orderDate[1] || null;
      delete queryForm.checkCompletionTime;
      delete queryForm.orderDate;
      return queryForm;
    },
    async getList() {
      try {
        this.loading = true;
        const queryForm = this.processingParams(this.queryParams);
        const res = await checkSummaryQuery(queryForm);
        const { data: list, total } = res.data.data;
        this.dataList = list;
        this.total = total;
        this.loading = false;
      } catch (error) {
        console.log(error, "error");
      } finally {
        this.loading = false;
      }
    },
    handleQuery() {
      this.queryParams.pageNo = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.pageNo = 1;
      this.handleQuery();
    },
    handleExport() {
      const queryForm = this.processingParams(this.queryParams);
      delete queryForm.pageNo;
      delete queryForm.pageSize;
      this.download(
        "check/summary/export",
        {},
        `对账结果汇总_${new Date().getTime()}.xlsx`,
        {},
        queryForm
      );
    },
    handleJump(row) {
      this.$router.push({
        path: "/errorManagement/discrepancy",
        query: {
          checkNo: row.checkNo,
          refresh: true,
        },
      });
    },
  },
};
</script>

<style></style>
