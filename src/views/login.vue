<template>
  <div class="login">
    <el-form key="1" v-if="!updateForm" ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
      <h3 class="title">KUN运营后台管理系统</h3>
      <el-form-item prop="username">
        <el-input
          v-model="loginForm.username"
          type="text"
          auto-complete="off"
          placeholder="账号"
        >
          <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="password">
        <el-input
          v-model="loginForm.password"
          type="password"
          auto-complete="off"
          placeholder="密码"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon" />
        </el-input>
      </el-form-item>
      <el-form-item prop="emailCode">
        <el-input
          v-model="loginForm.emailCode"
          auto-complete="off"
          placeholder="邮箱验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <el-button v-if="!countDownIng" @click="getEmailCode(loginForm.username, 'ADMIN_LOGIN')" :loading="emailLoading" class="login-code-img">获取验证码</el-button>
          <el-button v-else class="login-code-img">{{countdown}}</el-button>
        </div>
      </el-form-item>
      <el-form-item prop="code" v-if="captchaEnabled">
        <el-input
          v-model="loginForm.code"
          auto-complete="off"
          placeholder="验证码"
          style="width: 63%"
          @keyup.enter.native="handleLogin"
        >
          <svg-icon slot="prefix" icon-class="validCode" class="el-input__icon input-icon" />
        </el-input>
        <div class="login-code">
          <img :src="codeUrl" @click="getCode" class="login-code-img"/>
        </div>
      </el-form-item>
      <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox>
      <el-form-item style="width:100%;">
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width:100%;"
          @click.native.prevent="handleLogin"
        >
          <span v-if="!loading">登 录</span>
          <span v-else>登 录 中...</span>
        </el-button>
        <div style="float: right;" v-if="register">
          <router-link class="link-type" :to="'/register'">立即注册</router-link>
        </div>
      </el-form-item>
    </el-form>
    <!-- 强制修改密码 -->
    <el-form key="2" v-else ref="form" :model="user" :rules="rules" class="login-form">
      <h3 class="title">{{updateFormTitle}}</h3>
      <el-form-item  prop="oldPassword">
        <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" show-password/>
      </el-form-item>
      <el-form-item  prop="newPassword">
        <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" show-password/>
      </el-form-item>
      <el-form-item  prop="confirmPassword">
        <el-input v-model="user.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
      </el-form-item>
      <el-form-item  prop="emailCode">
        <el-input v-model="user.emailCode" placeholder="邮箱验证码" style="width: 63%"/>
        <div class="login-code" style="width: 32%">
          <el-button v-if="!countDownIng" :loading="emailLoading" @click="getEmailCode(loginForm.username, 'MODIFY_PASSWORD')" class="login-code-img">获取验证码</el-button>
          <el-button style="width:100px" v-else class="login-code-img">{{countdown}}</el-button>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="medium" @click="submit">保存</el-button>
        <!-- <el-button type="plain" size="medium" @click="back">返回</el-button> -->
      </el-form-item>
    </el-form>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>kun</span>
    </div>
  </div>
</template>

<script>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from '@/utils/jsencrypt'
import { getToken, setToken } from '@/utils/auth'
import { getEnums } from '@/api/enums'
import { setAllDicts } from '@/utils/storage'
import { sendEmailCode ,verifyPwdStatus ,updateUserPwdByEmail} from "@/api/system/user";
export default {
  name: "Login",
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      codeUrl: "",
      loginForm: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
        emailCode: "",
      },
      loginRules: {
        username: [
          { required: true, trigger: "blur", message: "请输入您的账号" }
        ],
        password: [
          { required: true, trigger: "blur", message: "请输入您的密码" }
        ],
        emailCode: [
          { required: true, trigger: "blur", message: "请输入邮箱验证码" },
          { min: 6, max: 6, message: "验证码长度为6位", trigger: "blur" }
        ],
        code: [
          { required: true, trigger: "blur", message: "请输入验证码" }
        ],
      },
      loading: false,
      token: '',
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      timer: null,
      emailLoading: false,
      countdown: 120,
      countDownIng: false,
      redirect: undefined,
      updateForm: false,
      updateFormTitle: "修改密码",
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined,
        emailCode: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 12, max: 20, message: "长度在 12 到 20 个字符", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ],
        emailCode:[
          { required: true, message: "邮箱验证码不能为空", trigger: "blur" },
          { min: 6, max: 6, message: "验证码长度为6位", trigger: "blur" }
        ]
      }
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    this.getCode();
    this.getCookie();
  },
  unmounted() {
    this.initCountDown()
  },
  methods: {
    // 重设密码
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 放
          setToken(this.token)
          this.$store.dispatch("SetToken", this.token)
          updateUserPwdByEmail(this.user.oldPassword, this.user.newPassword, this.user.emailCode).then(response => {
            //移
            setToken('')
            this.$store.dispatch("SetToken", '')
            const res = response
            if(res.code == 200){
              this.$modal.msgSuccess("修改密码成功");
              this.updateForm = false;
              // 设置Token
              setToken(this.token)
              // 更新store
              this.$store.dispatch("SetToken", this.token).finally(() => {
                this.token = ''
                // 进入主页
                this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
              });
            } else {
              this.$modal.msgError(res.msg);
            }
          });
        }
      });

    },
    back() {
      this.updateForm = false;
    },
    initCountDown(){
      clearInterval(this.timer);
      this.countdown = 120;
      this.countDownIng = false;
    },
    formReset(ref) {
      this.$refs[ref].resetFields();
    },
    getCode() {
      getCodeImg().then(res => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled;
        if (this.captchaEnabled) {
          this.codeUrl = "data:image/gif;base64," + res.img;
          this.loginForm.uuid = res.uuid;
        }
      });
    },
    getEmailCode(username, verifyCodeType) {
      let isOk = true
      // 重设密码时已经输入了用户名
      this.$refs.loginForm?.validateField("username", valid => {
        if(!valid){
          isOk = false
        }
      })
      if(!this.loginForm.username && isOk) return
      
      this.emailLoading = true;
      sendEmailCode({
        username, 
        verifyCodeType
      }).then(res => {
        const {code, msg} = res;
        // 倒计时逻辑
        // 验证码已发送
        if(code === 200){
          this.$modal.msgSuccess("验证码已发送");
          // 页面倒计时逻辑
          this.countDownIng = true;
          // 计时器
          this.timer = setInterval(() => {
            this.countdown--;
            if (this.countdown <= 0) {
              this.initCountDown()
            }
          }, 1000);
          
        }else{
          this.$modal.msgError(msg||"验证码发送失败，请重试");
        }
        this.emailLoading = false;
      }).finally(() => {
        this.emailLoading = false;
      });
    },
    getCookie() {
      const username = Cookies.get("username");
      const password = Cookies.get("password");
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      };
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          if (this.loginForm.rememberMe) {
            Cookies.set("username", this.loginForm.username, { expires: 30 });
            Cookies.set("password", encrypt(this.loginForm.password), { expires: 30 });
            Cookies.set('rememberMe', this.loginForm.rememberMe, { expires: 30 });
          } else {
            Cookies.remove("username");
            Cookies.remove("password");
            Cookies.remove('rememberMe');
          }
          this.$store.dispatch("Login", this.loginForm).then(() => {
            getEnums().then(res => {
              setAllDicts(res.data.data)
            })
            this.initCountDown()
          }).then((res)=>{
            // 校验密码状态
            verifyPwdStatus().then(response => {
              if(response.code === 200){
                this.loading = false;
                this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
              }else{
                this.loading = false;
                this.token = getToken()
                // 清除token
                this.$store.dispatch("FedLogOut")
                // 清除计时器
                this.initCountDown()
                // 设置修改密码弹窗标题
                if( response.code === 416) this.updateFormTitle = "首次登录,请修改密码"
                if( response.code === 417) this.updateFormTitle = "超过90天未修改密码,请修改"
                // 显示修改密码弹窗
                this.updateForm = true
              }
            });
          }).catch(() => {
            console.log('登录失败');
            this.loading = false;
            if (this.captchaEnabled) {
              this.getCode();
            }
          });
        }
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.png");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
    ::v-deep .el-input__inner {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
  width: 100%;
}
</style>
