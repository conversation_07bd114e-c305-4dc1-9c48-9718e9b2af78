<script lang="js">
import { defineComponent } from '@vue/composition-api'
import { fetchSchema } from '@/utils/lowcode'
export default defineComponent({
  data() {
    return {
      pageCode: 'warehouse_transfer_record',
      schema: {} 
    }
  },
  async mounted() {
    this.schema = await fetchSchema(this.pageCode)
  }
})
</script>
<template>
  <div class="kun-content">
    <low-code-renderer v-if="schema.components" :key="pageCode" :schema="schema"/>
  </div>
</template>
<style scoped>
.ant-modal-wrap{
  z-index: 1001  !important;
}
.yee-confirm-wrapper{
  z-index: 1002  !important;
}
</style>
