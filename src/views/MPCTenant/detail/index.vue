<template>
  <div v-loading="loading" class="app-container mpc-detail">
    <h1 class="title">MPC租户详情</h1>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="box-card">
          <div>
            <h4>当前已使用钱包</h4>
            <h3 class="amount">{{ baseInfo.usedWalletNum }}个</h3>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card class="box-card">
          <div>
            <h4>剩余可使用钱包</h4>
            <h3 class="amount">{{ baseInfo.remainWalletNum }}个</h3>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-descriptions class="tenant-title" title="租户信息" :column="2" border>
      <el-descriptions-item label="租户名称">{{
        baseInfo.tenantName
      }}</el-descriptions-item>
      <el-descriptions-item label="租户ID">{{
        baseInfo.tenantId
      }}</el-descriptions-item>
      <el-descriptions-item label="最大钱包数量">{{
        baseInfo.maxWalletNum
      }}</el-descriptions-item>
    </el-descriptions>

    <h3 class="title">钱包信息</h3>

    <el-table :data="baseInfo.tenantWallet">
      <el-table-column width="200" label="链路" prop="baseSymbol">
        <template slot-scope="scope">
          <dict-tag :options="chains" :value="scope.row.baseSymbol" />
        </template>
      </el-table-column>
      <el-table-column label="归集地址" prop="aggAddress"></el-table-column>
      <el-table-column
        label="提现地址（多地址英文逗号,隔开）"
        prop="withdrawAddress"
      ></el-table-column>
    </el-table>
  </div>
</template>
<script>
import { tenantDetail } from "@/api/mpcTenant";
import { findDictsByType } from "@/utils/storage";

export default {
  name: "MPCTenantDetail",
  data() {
    return {
      loading: true,
      hasInt: true,
      chains: findDictsByType("mpc_chains"),
      baseInfo: {
        usedWalletNum: null,
        remainWalletNum: null,
        tenantName: null,
        tenantId: null,
        maxWalletNum: null,
        tenantWallet: [],
      },
    };
  },
  async created() {
    this.tenantId = this.$route.query.tenantId;
    await this.getData();
  },
  async activated() {
    this.tenantId = this.$route.query.tenantId;
    if (!this.hasInt) {
      await this.getData();
    }
  },
  methods: {
    async getData() {
      try {
        const data = await tenantDetail({
          tenantId: this.tenantId,
        });
        this.baseInfo = data.data?.data;
        if (!this.baseInfo.tenantWallet) {
          this.baseInfo.tenantWallet = [];
        }
      } catch (error) {
      } finally {
        this.loading = false;
        this.hasInt = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.mpc-detail {
  min-height: calc(100vh - 84px - 40px);
  .title {
    margin-bottom: 20px;
  }
  .amount {
    text-align: center;
    margin: 40px;
  }
  .tenant-title {
    margin: 40px 0;
  }
}
</style>
