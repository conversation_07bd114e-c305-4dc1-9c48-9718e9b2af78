<template>
  <el-dialog
    title="租户钱包配置"
    :visible.sync="visible"
    width="1000px"
    custom-class="el-dialog-underline"
    @close="closeModal"
    top="5vh"
    :close-on-click-modal="false"
    append-to-body
  >
    <div style="display: flex; align-items: center; margin-bottom: 8px">
      <div style="margin: 0; margin-right: 8px; font-size: 16px">租户名称:</div>
      <div style="font-size: 16px">{{ name }}</div>
    </div>

    <h4 style="margin: 20px 0">归集地址配置</h4>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="rules"
      size="small"
      :inline="true"
      label-width="110px"
      class="asset-account-bookkeeping"
    >
      <el-table
        :data="ruleForm.tableData"
        style="width: 100%"
        :header-cell-style="{ 'background-color': '#fafafa' }"
      >
        <el-table-column width="300" label="链路选择">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.baseSymbol'"
              :rules="rules.baseSymbol"
            >
              <el-select
                v-model="scope.row.baseSymbol"
                style="width: 100%"
                placeholder="请选择链路"
                clearable
              >
                <el-option
                  v-for="chain in chains"
                  :key="chain.value"
                  :label="chain.label"
                  :value="chain.value"
                />
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column label="提现地址（最大3个,用英文逗号隔开）">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.withdrawAddress'"
              :rules="rules.withdrawAddress"
            >
              <el-input
                style="width: 100%"
                v-model.trim="scope.row.withdrawAddress"
                placeholder="请输入提现地址"
                clearable
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template slot-scope="scope">
            <span
              v-if="ruleForm.tableData.length > 1"
              class="delete-btn"
              @click="deleteLine(scope.$index)"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div
        v-show="ruleForm.tableData.length < chains.length"
        class="footer"
        @click="addLine"
      >
        <i class="el-icon-plus" />
        新增
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>
<script>
import { updateTenantConfig } from "@/api/mpcTenant";
import { findDictsByType } from "@/utils/storage";
import { deepClone } from "@/utils";

export default {
  data() {
    // 校验提现地址格式（只能英文、数字和英文逗号）
    const validateWithdrawAddress = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("提现地址不能为空"));
      }
      // 校验是否只包含英文、数字和英文逗号
      if (!/^[a-zA-Z0-9,]+$/.test(value)) {
        return callback(new Error("只能包含英文、数字和英文逗号"));
      }
      // 校验逗号数量不超过2个（最多3个地址）
      if ((value.match(/,/g) || []).length > 2) {
        return callback(new Error("最多只能输入3个地址"));
      }
      if (value.length > 256) {
        return callback(new Error("不能超过256个字符"));
      }

      callback();
    };
    // 校验链路唯一性
    const validateChainUnique = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("链路不能为空"));
      }
      // 检查当前链路是否已被其他行选择
      const selectedChains = this.ruleForm.tableData
        .map((item) => item.baseSymbol)
        .filter((baseSymbol) => baseSymbol);

      if (selectedChains.indexOf(value) !== selectedChains.lastIndexOf(value)) {
        return callback(new Error("该链路已被选择"));
      }
      callback();
    };
    return {
      visible: false,
      loading: false,
      tenantId: "",
      type: "",
      name: "",
      originData: [], // 需要记录原始数据，区分更新和删除
      ruleForm: {
        tableData: [
          {
            baseSymbol: "",
            withdrawAddress: "",
          },
        ],
      },
      chains: findDictsByType("mpc_chains"),
      rules: {
        baseSymbol: [
          {
            required: true,
            message: "链路不能为空",
            trigger: ["change", "blur"],
          },
          { validator: validateChainUnique, trigger: ["change", "blur"] },
        ],
        withdrawAddress: [
          {
            required: true,
            message: "提现地址不能为空",
            trigger: ["change", "blur"],
          },
          { validator: validateWithdrawAddress, trigger: ["change", "blur"] },
        ],
      },
    };
  },
  methods: {
    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.ruleForm.tableData = [
        {
          baseSymbol: "",
          withdrawAddress: "",
        },
      ];
    },
    closeModal() {
      this.resetForm();
      this.visible = false;
    },
    addLine() {
      this.ruleForm.tableData.push({
        baseSymbol: "",
        withdrawAddress: "",
      });
    },
    deleteLine(index) {
      this.ruleForm.tableData.splice(index, 1);
    },
    show(tenantId, name, data) {
      this.name = name;
      if (data && data.length) {
        this.type = "update";
        this.ruleForm.tableData = data;
        this.originData = deepClone(data);
      } else {
        this.type = "add";
        this.originData = [];
      }
      this.tenantId = tenantId;
      this.visible = true;
    },
    handleConfirm() {
      this.$refs["ruleForm"].validate(async (valid) => {
        if (valid) {
          this.loading = true;
          try {
            const tableData = this.ruleForm.tableData;

            const originData = this.originData; // 原始数据（含id）

            const delList = originData
              .filter(
                (originItem) =>
                  !tableData.some((item) => item.id === originItem.id)
              )
              .map((item) => item.id);

            const updList = tableData.filter(
              (item) => !delList.find((deletedId) => deletedId === item.id)
            );

            const data = {
              tenantId: this.tenantId,
              delList,
              updList,
            };

            await updateTenantConfig(data);
            this.$message.success("租户钱包配置成功");
            this.resetForm();
            this.$emit("success");
            this.visible = false;
          } catch (error) {
            console.log(error, "error");
          } finally {
            this.loading = false;
          }
        } else {
          return false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  display: flex;
  align-items: center;
}

.footer {
  color: rgba(0, 0, 0, 0.65);
  border: 1px dashed #efefef;
  padding: 4px;
  margin: 8px 0;
  text-align: center;
  cursor: pointer;

  &:hover {
    border: 1px dashed #1890ff;
    color: #1890ff;
  }
}

.delete-btn {
  display: inline-block;
  cursor: pointer;
  margin-bottom: 18px;
}

::v-deep .el-form-item {
  width: 100%;

  .el-form-item__content {
    width: 100%;

    & > .el-input,
    & > .el-select {
      width: 90%;
    }
  }
}
</style>
