<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="600px"
    custom-class="el-dialog-underline"
    @close="closeModal"
    top="5vh"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form :model="form" :rules="rules" ref="tenantForm" size="small">
      <el-form-item label="租户名称" required prop="tenantName">
        <el-input
          v-model.trim="form.tenantName"
          maxlength="10"
          show-word-limit
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="最大钱包配置数量" required prop="maxWalletNum">
        <el-input v-model.trim="form.maxWalletNum" clearable> </el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button type="primary" :loading="loading" @click="handleConfirm"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { addTenant, updateTenant } from "@/api/mpcTenant";

export default {
  data() {
    // 自定义校验规则：必须为大于0的数字
    const validateAmount = (rule, value, callback) => {
      if (!value) {
        callback(new Error("最大钱包配置数量不能为空"));
      } else if (!/^[1-9]\d*$/.test(value)) {
        callback(new Error("必须输入大于0的整数"));
      } else {
        const numValue = Number(value);
        if (numValue > 1000000) {
          callback(new Error("输入值不能超过100万"));
        } else {
          callback();
        }
      }
    };
    return {
      title: "新增租户配置",
      visible: false,
      loading: false,
      tenantId: "",
      form: {
        tenantName: "",
        maxWalletNum: "",
      },
      rules: {
        tenantName: [
          {
            required: true,
            message: "租户名称不能为空",
            trigger: ["change", "blur"],
          },
        ],
        maxWalletNum: [
          { validator: validateAmount, trigger: ["change", "blur"] },
        ],
      },
    };
  },
  methods: {
    closeModal() {
      this.$refs.tenantForm.resetFields();
      this.form = {
        tenantName: "",
        maxWalletNum: "",
      };
      this.visible = false;
    },
    show(data) {
      if (data) {
        this.tenantId = data.tenantId;
        Object.keys(this.form).forEach((key) => {
          this.form[key] = data[key];
        });
        this.title = "编辑租户配置";
      } else {
        this.tenantId = "";
        this.title = "新增租户配置";
      }

      this.visible = true;
    },
    handleConfirm() {
      this.$refs["tenantForm"].validate(async (valid) => {
        if (valid) {
          if (this.tenantId) {
            await updateTenant({
              tenantId: this.tenantId,
              ...this.form,
            });
            this.$message.success("编辑租户成功");
          } else {
            await addTenant({
              ...this.form,
            });
            this.$message.success("新增租户成功");
          }
          this.$refs.tenantForm.resetFields();
          this.visible = false;
          this.$emit("success");
        } else {
          return false;
        }
      });
    },
  },
};
</script>
