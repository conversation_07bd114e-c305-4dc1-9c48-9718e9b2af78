<template>
  <div class="app-container mpc-tenant">
    <el-row>
      <el-col>
        <el-button type="primary" @click="addTenant">添加MPC租户</el-button>
      </el-col>
    </el-row>
    <el-table v-loading="loading" :data="list" style="margin-top: 20px">
      <el-table-column prop="tenantId" label="租户ID"></el-table-column>
      <el-table-column prop="tenantName" label="租户名称"></el-table-column>
      <el-table-column prop="usedWalletNum" label="钱包使用"></el-table-column>
      <el-table-column label="钱包管理">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="editWallet(scope.row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="租户钱包信息">
        <template slot-scope="scope">
          <router-link
            class="detail"
            :to="
              '/MPCTenantManage/MPCTenantDetail?tenantId=' + scope.row.tenantId
            "
            >查看详情</router-link
          >
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="editTenant(scope.row)"
            >编辑</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <AddModal ref="addModal" @success="getList" />
    <WalletModal ref="walletModal" @success="getList" />
  </div>
</template>

<script>
import AddModal from "./addModal.vue";
import WalletModal from "./walletModal.vue";
import { tenantList, tenantDetail } from "@/api/mpcTenant";
export default {
  name: "MPCTenantConfiguration",
  components: {
    AddModal,
    WalletModal,
  },
  data() {
    return {
      queryParams: {
        pageNo: 1,
        pageSize: 20,
      },
      total: 1,
      list: [],
      loading: false,
    };
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      this.loading = true;
      try {
        const data = await tenantList();
        this.list = data.data?.data;
      } catch (error) {
        console.log(error, "err");
      } finally {
        this.loading = false;
      }
    },
    addTenant() {
      this.$refs.addModal.show();
    },
    async editTenant(row) {
      const data = await tenantDetail({ tenantId: row.tenantId });
      this.$refs.addModal.show(data.data?.data);
    },
    async editWallet(row) {
      const data = await tenantDetail({ tenantId: row.tenantId });
      this.$refs.walletModal.show(row.tenantId, row.tenantName, data.data?.data?.tenantWallet);
    },
  },
};
</script>

<style lang="scss" scoped>
.mpc-tenant {
  min-height: calc(100vh - 84px - 40px);
}
.detail {
  text-decoration: underline;
  color: #409eff;
}
</style>
